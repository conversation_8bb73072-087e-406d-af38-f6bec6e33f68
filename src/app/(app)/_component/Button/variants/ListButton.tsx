//Strictly for DRAWER mobile buttons

import { ClassValue } from 'clsx'
import { PropsWithChildren } from 'react'
import { cn } from '@/lib/utils'

interface Props {
  isActive?: boolean
  onClick?: () => void
  className?: ClassValue
}

function ListButton(props: PropsWithChildren<Props>) {
  const { onClick, isActive, className, children } = props
  return (
    <button
      className={cn(
        'flex flex-row gap-2 items-center border-black bg-white hover:bg-accent2 py-4 px-4',
        className,
      )}
      onClick={onClick}
    >
      {children}
    </button>
  )
}

export default ListButton
