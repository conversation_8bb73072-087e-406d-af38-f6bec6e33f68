import { PropsWithChildren } from 'react'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '@/app/(app)/_cssComp/FlexContainer'
import { cn } from '@/lib/utils'
import Star12 from '../../Design/Star'
import Text from '../../Text'
import UnderlineHighlight from '../../Text/UnderlineHighlight'

interface Props {
  title?: string
  subtitle?: string
  highlight?: boolean
  star?: boolean
  price?: string
  className?: string
}

function CardPrice(props: PropsWithChildren<Props>) {
  const { title, subtitle, price, highlight, star, className, children } = props
  return (
    <div
      className={cn(
        `w-full border-black border-2 rounded-sm shadow-[3px_3px_0px_rgba(0,0,0,1)] bg-accent2-lighter p-8 relative`,
        className,
      )}
    >
      <FlexContainer direction={FlexDirection.COL} className="gap-8 w-full">
        <FlexContainer
          direction={FlexDirection.COL}
          justify={JustifyContent.CENTER}
          align={AlignItems.CENTER}
          className={'w-full gap-8'}
        >
          <FlexContainer
            direction={FlexDirection.COL}
            justify={JustifyContent.CENTER}
            align={AlignItems.CENTER}
            className={'w-full gap-1'}
          >
            <Text variant="emphasis" size="2xl" className="relative overflow: visible">
              {star && (
                <Star12
                  className="absolute top-0 right-0 -translate-y-1/2 translate-x-3/4"
                  size={108}
                  stroke="black"
                  strokeWidth={2}
                  color="yellow"
                />
              )}
              {highlight ? <UnderlineHighlight>{title}</UnderlineHighlight> : <>{title}</>}
            </Text>
            <Text variant="description" size="base" className={'text-center'}>
              {subtitle}
            </Text>
          </FlexContainer>
          <Text variant="emphasis" size="2xl" className={'text-6xl'}>
            {price}
          </Text>
        </FlexContainer>
        <FlexContainer>{children}</FlexContainer>
      </FlexContainer>
    </div>
  )
}

export default CardPrice
