'use client'

import { <PERSON>psWithChildren, useEffect } from 'react'
import { PropagateLoader } from 'react-spinners'

import FlexContainer, { AlignItems, JustifyContent } from '../_cssComp/FlexContainer'
import { useAuthState } from '../_state/authState'

const isTokenExpired = (expiry: number | null): boolean => {
  if (!expiry) return true
  return Date.now() >= expiry
}

/**
 * This hook is a wrapper around useAuthState, and manages the retrieval of the user data from the server.
 * It will automatically update the user state in the store when the token is refreshed, and will also force a logout when the token is expired.
 *
 * The hook returns an object with the following properties:
 *
 * - user: The user data stored in the store.
 * - isLoading: A boolean indicating whether the user data is being retrieved from the server.
 *
 * The hook also takes care of the following:
 *
 * - If the user is not logged in, it will set the user state in the store to null.
 * - If the user is logged in and the token is expired, it will force a logout.
 * - If the user is logged in and the token is not expired, it will retrieve the user data from the server and update the user state in the store.
 * - Delete from IndexedDB
 *
 * This hook is intended to be used in the place of useAuthState, and should be used in all places where the user data is needed.
 */
export function useAuthUser() {
  const user = useAuthState((state) => state.user)
  const isLoading = useAuthState((state) => state.isLoading)
  const expiry = useAuthState((state) => state.expiry)
  const isLoggedIn = useAuthState((state) => state.isLoggedIn)
  const hasEverLoggedIn = useAuthState((state) => state.hasEverLoggedIn)
  const loadUser = useAuthState((state) => state.loadUser)
  const isRehydrated = useAuthState((state) => state.rehydrated)

  useEffect(() => {
    if (isRehydrated && user == null && hasEverLoggedIn) {
      loadUser()
    }
  }, [isRehydrated, user, hasEverLoggedIn, loadUser])

  useEffect(() => {
    if (user && isLoggedIn && expiry && isTokenExpired(expiry)) {
      // TODO: handle refresh logic here
    }
  }, [user, isLoggedIn, expiry])

  return {
    user,
    hasEverLoggedIn: hasEverLoggedIn,
    isLoggedIn: isLoggedIn,
    isLoading: isLoading,
    isRehydrated: isRehydrated,
  }
}

function AuthProvider({ children }: PropsWithChildren) {
  const { isLoading } = useAuthUser()
  const rehydrated = useAuthState((state) => state.rehydrated)

  if (!rehydrated) {
    return (
      <FlexContainer align={AlignItems.CENTER} justify={JustifyContent.CENTER} className="h-screen">
        <PropagateLoader />
      </FlexContainer>
    )
  }

  return isLoading ? (
    <FlexContainer align={AlignItems.CENTER} justify={JustifyContent.CENTER} className="h-screen">
      <PropagateLoader />
    </FlexContainer>
  ) : (
    children
  )
}

export default AuthProvider
