import { NextResponse } from 'next/server'
// import passport from 'passport'
// import GoogleOAuthStrategy from '@/app/(app)/_backend/common/service/UserService/AuthStrategies/GoogleOAuthStrategy'

// Initialize strategy once outside the handler
// passport.use("googleOAuth", GoogleOAuthStrategy);

export async function GET() {
  const params = new URLSearchParams({
    client_id: process.env.GOOGLE_CLIENT_ID!,
    redirect_uri: process.env.GOOGLE_CALLBACK_URL!,
    response_type: 'code',
    scope: 'profile email',
    access_type: 'offline',
    prompt: 'consent',
  })

  const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`
  return NextResponse.redirect(googleAuthUrl)
}
