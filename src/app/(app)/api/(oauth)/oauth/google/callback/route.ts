import * as jose from 'jose'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import { getPayload, getCookieExpiration } from 'payload'
import * as crypto from 'crypto'
import UserDAO from '@/app/(app)/_backend/common/dao/UserDAO'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import config from '@payload-config'

export async function GET(request: Request) {
  console.log('callback url')
  // 1. Extract code from query params
  const url = new URL(request.url)
  const code = url.searchParams.get('code')
  if (!code) {
    return new NextResponse('Missing code', { status: 400 })
  }

  // 2. Exchange code for tokens and user info
  const tokenRes = await fetch('https://oauth2.googleapis.com/token', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: new URLSearchParams({
      code,
      client_id: process.env.GOOGLE_CLIENT_ID!,
      client_secret: process.env.GOOGLE_CLIENT_SECRET!,
      redirect_uri: process.env.GOOGLE_CALLBACK_URL!,
      grant_type: 'authorization_code',
    }),
  })
  const tokenData = await tokenRes.json()
  if (!tokenData.access_token) {
    return new NextResponse('Failed to get access token', { status: 400 })
  }

  // 3. Get user info from Google
  const userRes = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
    headers: { Authorization: `Bearer ${tokenData.access_token}` },
  })
  const { email, given_name, family_name } = await userRes.json()
  console.log(email, given_name, family_name)

  // 4. Find or create user in your DB
  const payload = await getPayload({ config })
  const userDAO = new UserDAO(payload)
  const userService = new UserService(userDAO)
  let user = await userService.getUserByEmailId(email)
  if (!user) {
    user = await userService.register(email, given_name, family_name, true)
  }

  // 5. Issue JWT
  const collectionConfig = payload.collections['public-users'].config
  const fieldsToSign = {
    id: user.id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    freeCredits: user.freeCredits,
    paidCredits: user.paidCredits,
    rolloverCredits: user.rolloverCredits,
    role: user.role,
    collection: collectionConfig.slug,
  }
  // Encode the secret key
  const secret = new TextEncoder().encode(payload.secret)

  // Calculate expiration timestamp
  const expirationTime = Math.floor(Date.now() / 1000) + collectionConfig.auth.tokenExpiration
  // Sign the token using jose
  const token = await new jose.SignJWT(fieldsToSign)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(expirationTime) // Use numeric timestamp
    .sign(secret)

  const expiresAt = getCookieExpiration({ seconds: collectionConfig.auth.tokenExpiration })
  const expTimestamp = Math.floor(expiresAt.getTime() / 1000)
  const authState = {
    state: {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        freeCredits: user.freeCredits,
        paidCredits: user.paidCredits,
        rolloverCredits: user.rolloverCredits,
        role: user.role,
      },
      hasEverLoggedIn: true,
      isLoggedIn: true,
      isLoading: false,
      error: null,
      expiry: expTimestamp,
    },
    version: 0,
  }

  // 7. Redirect or respond
  const origin =
    request.headers.get('origin') || process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'
  const response = NextResponse.redirect(origin + '/')
  response.cookies.set('auth-storage', JSON.stringify(authState), {
    sameSite: 'lax',
    secure: process.env.NODE_ENV === 'production',
    expires: getCookieExpiration({ seconds: collectionConfig.auth.tokenExpiration }),
  })
  response.cookies.set('la-token', token, {
    httpOnly: true,
    sameSite: 'lax',
    secure: process.env.NODE_ENV === 'production',
    expires: getCookieExpiration({ seconds: collectionConfig.auth.tokenExpiration }),
  })
  return response
}
