import { getPayload } from 'payload'
import GeneratedImageDAO from '@/app/(app)/_backend/common/dao/GeneratedImageDAO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import ImageService from '@/app/(app)/_backend/common/service/ImageService'
import config from '@payload-config'

type AspectRatio = '1:1' | '2:3' | '3:2'

interface RemoveBackgroundRequest {
  imageBase64: string
  aspectRatio: AspectRatio
}

export interface RemoveBackgroundResponse {
  status: number
}

export async function POST(request: Request) {
  try {
    const { imageBase64, aspectRatio } = (await request.json()) as RemoveBackgroundRequest

    const userHeader = request.headers.get('x-user')
    const user = JSON.parse(userHeader!)

    const payload = await getPayload({ config })
    const generatedImageDAO = new GeneratedImageDAO(payload)

    const imageService = new ImageService(generatedImageDAO)
    imageService.removeBackground(imageBase64, aspectRatio, user.id)

    // note: not returning the image
    return new Response(JSON.stringify({ message: 'Generating images...' }), {
      status: 202,
    })
  } catch (error) {
    return errorHandler(error)
  }
}
