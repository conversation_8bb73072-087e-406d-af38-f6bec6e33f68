import { getPayload } from 'payload'
import GeneratedImageDAO from '@/app/(app)/_backend/common/dao/GeneratedImageDAO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import ImageService from '@/app/(app)/_backend/common/service/ImageService'
import config from '@payload-config'

interface ImageUpscaleRequest {
  b64: string
  upscale: number
}

export interface ImageUpscaleResponse {
  src: string
  width: number
  height: number
}

export async function POST(request: Request) {
  try {
    const { b64, upscale } = (await request.json()) as ImageUpscaleRequest

    const userHeader = request.headers.get('x-user')
    const user = JSON.parse(userHeader!)

    const payload = await getPayload({ config })
    const generatedImageDAO = new GeneratedImageDAO(payload)

    const imageService = new ImageService(generatedImageDAO)
    const imageUpscaled = await imageService.upscaleImage(b64, upscale, user.id)
    return new Response(
      JSON.stringify({ message: 'Image has been successfully upscaled!', data: imageUpscaled }),
    )
  } catch (error) {
    errorHandler(error)
  }
}
