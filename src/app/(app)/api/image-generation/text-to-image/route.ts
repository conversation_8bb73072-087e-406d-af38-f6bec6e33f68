import { getPayload } from 'payload'
import GeneratedImageDAO from '@/app/(app)/_backend/common/dao/GeneratedImageDAO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import ImageService, { T2IAspectRatio } from '@/app/(app)/_backend/common/service/ImageService'
import config from '@payload-config'

interface ImageGenerationRequestT2T {
  prompt: string
  aspectRatio: T2IAspectRatio
  numberOfImages: number
  highQuality: boolean
}

export interface ImageGenerationResponseT2I {
  status: number
}

export async function POST(request: Request) {
  try {
    const { prompt, aspectRatio, numberOfImages, highQuality } =
      (await request.json()) as ImageGenerationRequestT2T
    const userHeader = request.headers.get('x-user')
    const user = JSON.parse(userHeader!)

    let quality = 'LOW_QUALITY' as 'LOW_QUALITY' | 'HIGH_QUALITY'
    if (highQuality) {
      quality = 'HIGH_QUALITY'
    }

    const payload = await getPayload({ config })
    const generatedImageDAO = new GeneratedImageDAO(payload)

    const imageService = new ImageService(generatedImageDAO)
    imageService.textToImage(prompt, aspectRatio, quality, numberOfImages, user.id)

    // note: not returning the image
    return new Response(JSON.stringify({ message: 'Generating images...' }), {
      status: 202,
    })
  } catch (error) {
    return errorHandler(error)
  }
}
