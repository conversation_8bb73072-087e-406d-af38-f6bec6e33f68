import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import User<PERSON><PERSON> from '@/app/(app)/_backend/common/dao/UserDAO'
import { User } from '@/app/(app)/_backend/common/dto/user/UserDTO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import config from '@payload-config'

export interface GetInfoRequest {
  email: string
}

export interface LoginResults {
  message: string
  data: User
}

export async function POST() {
  try {
    const cookieStore = cookies()
    const authToken = (await cookieStore).get('la-token')?.value
    const payload = await getPayload({ config })
    const userDAO = new UserDAO(payload)
    const userService = new UserService(userDAO)
    const verifyUser = await userService.verifyUser(authToken)
    const data = await userService.getUserByEmailId(verifyUser.email)
    return new Response(JSON.stringify({ message: 'User successfully retrieved!', data: data }))
  } catch (error) {
    return errorHandler(error)
  }
}
