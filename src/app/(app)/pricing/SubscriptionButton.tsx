'use client'

import { BadgePlus, Router } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, C<PERSON>Loader } from 'react-spinners'
import { toast } from 'sonner'
import { Button } from '@/app/(app)/_component/Button'
import FlexContainer, { AlignItems, FlexDirection, JustifyContent } from '../_cssComp/FlexContainer'
import { createStripeCheckoutSession } from '../_localApi/stripeClient'
import { useAuthUser } from '../_provider/AuthProvider'

interface Props {
  subscriptionName: string
  subscriptionDuration: 'year' | 'month'
}

function SubscriptionButton(props: Props) {
  const { subscriptionName, subscriptionDuration } = props
  const { user, hasEverLoggedIn, isRehydrated } = useAuthUser()
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const handleSubscribe = async () => {
    setIsLoading(true)
    //Case 1: User has logged in before, but currently logged out
    if (!user && hasEverLoggedIn) {
      router.push('/login')
      return
    }
    //Case 2: User has never logged in before, new user
    if (!user && !hasEverLoggedIn) {
      router.push('/register')
      return
    }

    //Case 3: Catch all, if for some reason, user is still empty
    if (!user) {
      router.push('/register')
      return
    }

    try {
      const createCheckoutSession = await createStripeCheckoutSession(
        user.id,
        subscriptionName,
        subscriptionDuration,
      )
      if (createCheckoutSession.success === false) {
        console.error('Stripe checkout session failed')
        toast.error('Stripe checkout failed. Please try again.', {
          position: 'top-right',
        })
      }

      if (createCheckoutSession.data.url) {
        router.push(createCheckoutSession.data.url)
      }
    } catch (error) {
      console.error(error)
      toast.error('Something went wrong. Please try again.', {
        position: 'top-right',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return !isRehydrated ? (
    <FlexContainer
      className={`w-full h-full gap-12`}
      direction={FlexDirection.ROW}
      align={AlignItems.CENTER}
      justify={JustifyContent.CENTER}
    >
      <BeatLoader />
    </FlexContainer>
  ) : (
    <Button
      variant={isLoading ? 'secondary' : 'emphasis'}
      size="xl"
      className="w-full"
      onClick={handleSubscribe}
    >
      {isLoading ? (
        <>
          <ClipLoader size={12} /> Subscribing...
        </>
      ) : (
        <>
          <BadgePlus /> Subscribe!
        </>
      )}
    </Button>
  )
}

export default SubscriptionButton
