'use client'

import { useMutation } from '@tanstack/react-query'
import { useQueryClient } from '@tanstack/react-query'
import Avatar from 'boring-avatars'
import { Coins, IdCard, LogOut, Settings, TriangleAlert } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useRef, useState } from 'react'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'
import { Spacing } from '@/utilities/local/css'
import Divider from '../../_component/Divider'
import { Progress } from '../../_component/Progress'
import Text from '../../_component/Text'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../_cssComp/FlexContainer'
import GridContainer from '../../_cssComp/GridContainer'
import { logoutUser } from '../../_localApi/users'
import { useAuthState } from '../../_state/authState'
// import { useImageState } from '../../_state/imageState'

interface Props {
  allowHover?: boolean
}

function CostMenuIndicator(props: Props) {
  const { allowHover = true } = props
  const queryClient = useQueryClient()
  const router = useRouter()
  const clientUser = useAuthState((state) => state.user)
  const paidCredits = clientUser?.paidCredits ?? 0
  const freeCredits = clientUser?.freeCredits ?? 0
  const rolloverCredits = clientUser?.rolloverCredits ?? 0
  const [onHover, setOnHover] = useState(false)
  const hoverTimeout = useRef<NodeJS.Timeout>(null)
  const HOST_URL = process.env.NEXT_PUBLIC_HOST ?? 'http://localhost:3000'
  const creditCostMap = {
    free: 100,
  }
  // const clearImages = useImageState((state) => state.clearImages)
  const logoutMutation = useMutation({
    mutationFn: () => {
      return logoutUser()
    },
    onSuccess: async () => {
      useAuthState.setState({
        user: null,
        isLoggedIn: false,
        expiry: null,
        hasEverLoggedIn: true,
      })
      queryClient.clear()
      // clearImages()
      router.push(HOST_URL)
    },
    onError: () => {
      toast.error('Logout failed', {
        description: 'Something went wrong trying to log you out. Please try again.',
        duration: 0,
        position: 'top-right',
        icon: <TriangleAlert />,
      })
    },
  })
  const handleLogout = async () => {
    await logoutMutation.mutateAsync()
  }
  return (
    <div className="relative w-full">
      <FlexContainer
        className={cn(
          'gap-4 w-full cursor-pointer p-2 rounded-md',
          `${allowHover ? 'hover:bg-accent' : ''}`,
        )}
        direction={FlexDirection.ROW}
        align={AlignItems.CENTER}
        onMouseEnter={() => {
          clearTimeout(hoverTimeout.current!)
          setOnHover(true)
        }}
        onMouseLeave={() => {
          hoverTimeout.current = setTimeout(() => setOnHover(false), 300)
        }}
      >
        <FlexContainer
          className="gap-2 flex-1"
          align={AlignItems.END}
          direction={FlexDirection.COL}
        >
          <FlexContainer
            className="gap-2"
            align={AlignItems.CENTER}
            justify={JustifyContent.END}
            direction={FlexDirection.ROW}
          >
            <Coins size="12px" />
            <Text size="sm">
              {paidCredits + freeCredits + rolloverCredits} / {creditCostMap['free']}
            </Text>
          </FlexContainer>
          <Progress
            value={paidCredits + freeCredits + rolloverCredits}
            max={creditCostMap['free']}
          />
        </FlexContainer>
        <Avatar size={40} name={clientUser?.firstName ?? ''} variant="beam" />
      </FlexContainer>
      {allowHover && onHover && (
        <div
          // className={cn(
          //   'absolute left-0 top-full mx-2 my-4 border-black border-2 bg-white shadow-lg rounded-lg opacity-100 transition-opacity',
          //   // 'max-w-[calc(100vw-1rem)]', // Prevents overflow
          //   // 'min-w-[200px] w-fit',
          //   'w-[min(90vw,240px)]',
          //   'left-1/2 -translate-x-1/2',
          //   // 'left-0 right-auto', // Default: Align left
          //   // 'lg:right-0 lg:left-auto', // When space is available, align right
          //   'overflow-hidden', // Ensures content stays within bounds
          //   Spacing.ContentPadding,
          // )}
          className={cn(
            'absolute top-full mt-4 border-black border-2 bg-white shadow-lg rounded-lg transition-opacity z-50',
            'left-1/2 -translate-x-1/2', // center it
            'max-w-[90vw] w-[240px]', // keep it responsive
            'overflow-hidden',
            Spacing.ContentPadding,
          )}
          onMouseEnter={() => clearTimeout(hoverTimeout.current!)}
          onMouseLeave={() => {
            hoverTimeout.current = setTimeout(() => setOnHover(false), 300)
          }}
        >
          <GridContainer className="w-[200px] grid-cols-1">
            <FlexContainer
              direction={FlexDirection.ROW}
              className="gap-2 px-2"
              align={AlignItems.CENTER}
            >
              <IdCard />
              <Text size="lg">{clientUser?.role}</Text>
            </FlexContainer>
            <Divider />
            <Link href={`${HOST_URL}/dashboard/settings`}>
              <FlexContainer
                direction={FlexDirection.COL}
                className="hover:border-black hover:bg-accent border-transparent cursor-pointer border-2 w-full gap-2 rounded-sm bg-white p-2"
              >
                <FlexContainer
                  direction={FlexDirection.ROW}
                  className="gap-2"
                  align={AlignItems.CENTER}
                >
                  <Settings />
                  <Text variant="emphasis" size="base">
                    Settings
                  </Text>
                </FlexContainer>
              </FlexContainer>
            </Link>
            <FlexContainer
              direction={FlexDirection.COL}
              className="hover:border-black hover:bg-accent border-transparent cursor-pointer border-2 w-full gap-2 rounded-sm bg-white p-2"
              onClick={handleLogout}
            >
              <FlexContainer
                direction={FlexDirection.ROW}
                className="gap-2"
                align={AlignItems.CENTER}
              >
                <LogOut />
                <Text variant="emphasis" size="base">
                  Logout
                </Text>
              </FlexContainer>
            </FlexContainer>
          </GridContainer>
        </div>
      )}
    </div>
  )
}

export default CostMenuIndicator
