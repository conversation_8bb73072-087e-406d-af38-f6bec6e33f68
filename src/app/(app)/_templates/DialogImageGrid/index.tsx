'use client'

import { ImagePlus } from 'lucide-react'
import NextImage from 'next/image'
import { useEffect, useRef, useState } from 'react'
import { RingLoader } from 'react-spinners'
import { v4 } from 'uuid'
import { cn } from '@/lib/utils'
import { fileToBase64, getImageDimensions, imageUrlToBase64 } from '@/utilities/local/image'
import { But<PERSON> } from '../../_component/Button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../../_component/Dialog'
import NoDataIllustratedMessage from '../../_component/IllustratedMessage/NoData'
import NoImageIllustratedMessage from '../../_component/IllustratedMessage/NoImage'
import { Input } from '../../_component/Input'
import ClientPagination from '../../_component/Pagination/ClientPagination'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../../_component/Tabs'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../_cssComp/FlexContainer'
import GridContainer from '../../_cssComp/GridContainer'
import { ImageMetadata } from '../../_types/image'
import { useImagesPagination } from '../../_hooks/useImagesPagination'

interface Props {
  onImageSelected?: (image: ImageMetadata) => void
  isDefaultSelection?: boolean
  isGenerationHistorySelection?: boolean
  isUserSelection?: boolean
  // Optional: pass images directly (for backward compatibility)
  images?: ImageMetadata[]
  // Optional: pagination settings
  pageSize?: number
}

function DialogImageGrid(props: Props) {
  const {
    onImageSelected,
    isDefaultSelection,
    isGenerationHistorySelection,
    isUserSelection,
    images: propsImages,
    pageSize = 5
  } = props

  // Use shared pagination hook if no images provided via props
  const {
    images: fetchedImages,
    pagination,
    isLoading: isFetchingImages,
  } = useImagesPagination({
    pageSize,
    enabled: !propsImages, // Only fetch if images not provided via props
  })

  // Use provided images or fetched images
  const images = propsImages || fetchedImages
  const isLoading = isFetchingImages
  const [openDialog, setOpenDialog] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const [selectedImage, setSelectedImage] = useState<string>()
  const [numberOfTabs, setNumberOfTabs] = useState<string>('grid-cols-3')
  const [imageFile, setImageFile] = useState<ImageMetadata | null>(null)
  const [paginatedImages, setPaginatedImages] = useState<ImageMetadata[]>([])
  const [activeTab, setActiveTab] = useState<string>('generationHistory')
  const [currentPage, setCurrentPage] = useState(1)
  const [currentPageSize, setCurrentPageSize] = useState(pageSize)

  useEffect(() => {
    if (images.length > 0) {
      // For client-side pagination when images are provided via props
      if (propsImages) {
        setPaginatedImages(images.slice(0, currentPageSize))
      } else {
        // For server-side pagination, display all fetched images
        setPaginatedImages(images)
      }
    }
  }, [images, currentPageSize, propsImages])

  useEffect(() => {
    let currentSize = 0
    if (isDefaultSelection) {
      currentSize += 1
    }
    if (isGenerationHistorySelection) {
      currentSize += 1
    }
    if (isUserSelection) {
      currentSize += 1
    }
    setNumberOfTabs(`grid-cols-${currentSize}`)
  }, [isDefaultSelection, isGenerationHistorySelection, isUserSelection])

  // useEffect(() => {
  //   if (hasHydrated && images.length === 0) {
  //     loadInitialImages()
  //   }
  // }, [hasHydrated, images.length])

  const handleImageSelection = (index: string) => {
    setSelectedImage(index)
  }

  const handleSelectImage = () => {
    if (activeTab === 'generationHistory') {
      if (!selectedImage || !onImageSelected || !images) return
      const imageSelected = images.find((image) => image.id === selectedImage)
      if (imageSelected) {
        onImageSelected(imageSelected)
        setOpenDialog(false)
      }
    } else if (activeTab === 'byobImage') {
      if (!imageFile || !onImageSelected) return
      onImageSelected(imageFile)
      setOpenDialog(false)
    }
  }

  const handleSelectImageDisabled = () => {
    if (activeTab === 'generationHistory') {
      return !selectedImage
    } else if (activeTab === 'byobImage') {
      return !imageFile
    }
  }

  const handlePaginationChange = (page: number, pageSize: number) => {
    if (propsImages) {
      // Client-side pagination for provided images
      const start = (page - 1) * pageSize
      const end = start + pageSize
      setPaginatedImages(images.slice(start, end))
    }
    // For server-side pagination, the useImagesPagination hook handles the fetching
    setCurrentPage(page)
    setCurrentPageSize(pageSize)
  }

  const handleUserUploadImage = async (event: React.ChangeEvent<HTMLInputElement>) => {
    let file = event.target.files?.[0]
    if (!file) return
    const base64 = await fileToBase64(file)
    const imageDimensions = await getImageDimensions(base64)
    const imageMetadata = {
      id: v4(),
      src: base64,
      width: imageDimensions.w,
      height: imageDimensions.h,
      prompt: '',
      actionType: '',
    }
    setSelectedImage(imageMetadata.id)
    setImageFile(imageMetadata)
  }

  const cleanupOnOpenCloseDialog = (isOpen: boolean) => {
    if (!isOpen) {
      setSelectedImage('')
      setImageFile(null)
      setCurrentPage(1)
      if (propsImages) {
        setPaginatedImages(images.slice(0, currentPageSize))
      }
      setOpenDialog(false)
    } else {
      setOpenDialog(true)
    }
  }

  const ImageGrid = () => {
    return paginatedImages?.map((image, index) => {
      const isActive = selectedImage === image.id
      const activeBorder = isActive
        ? 'border-accent2 shadow-accent2-lighter shadow-md'
        : 'border-black'
      return (
        <div
          key={index}
          className={`relative w-full aspect-square border-4 rounded ${activeBorder}`}
        >
          <NextImage
            key={image.id}
            style={{
              objectFit: 'contain',
              borderRadius: '20px',
            }}
            src={image.src}
            fill
            alt={''}
            onClick={() => handleImageSelection(image.id)}
          />
        </div>
      )
    })
  }

  const DisplayGenerationHistory = () => {
    if (isLoading && images.length == 0) {
      return (
        <div className="flex items-center justify-center h-full">
          <RingLoader />
        </div>
      )
    } else if (images.length === 0 && !isLoading) {
      return (
        <NoDataIllustratedMessage
          className="w-[35%] h-[15%] mx-auto my-auto"
          title="Nothing to see here!"
          description="Try generating some images, and come back again!"
        />
      )
    } else {
      return (
        <FlexContainer>
          <ClientPagination
            totalCount={propsImages ? images.length : pagination.totalCount}
            currentPage={currentPage}
            currentPageSize={currentPageSize}
            setCurrentPageSize={(newPageSize) => {
              setCurrentPageSize(newPageSize)
              handlePaginationChange(1, newPageSize) // Reset to page 1 when page size changes
            }}
            setCurrentPage={handlePaginationChange}
          />
          <GridContainer
            ref={containerRef}
            className="w-full h-full overflow-auto gap-5 p-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
          >
            <ImageGrid />
          </GridContainer>
        </FlexContainer>
      )
    }
  }

  return (
    <Dialog open={openDialog} onOpenChange={cleanupOnOpenCloseDialog}>
      <Button variant="positive" onClick={() => setOpenDialog(true)}>
        Select an Image
      </Button>
      <DialogContent className="w-[95%] md:w-[80%] lg:w-[70%] z-501 max-h-[90%] mx-auto">
        <DialogHeader>
          <DialogTitle>Image Gallery</DialogTitle>
          <DialogDescription>Select an image</DialogDescription>
        </DialogHeader>
        <Tabs value={activeTab} onValueChange={(val) => setActiveTab(val)}>
          <TabsList className={cn(`grid w-full grid-cols-3`, numberOfTabs)}>
            {isGenerationHistorySelection && (
              <TabsTrigger value="generationHistory">Generation History</TabsTrigger>
            )}
            {isDefaultSelection && <TabsTrigger value="predefined">Pre-defined Images</TabsTrigger>}
            {isUserSelection && <TabsTrigger value="byobImage">Bring your own image</TabsTrigger>}
          </TabsList>
          <TabsContent value="generationHistory" className="w-full">
            <div className="max-h-[70vh] overflow-y-auto p-4">
              {/* {images.length === 0 && !imageQuery.isLoading ? (
                <NoDataIllustratedMessage
                  className="w-[40%] h-[15%] mx-auto my-auto"
                  title="Nothing to see here!"
                  description="Try generating some images, and come back again!"
                />
              ) : (
                <FlexContainer>
                  <ClientPagination
                    totalCount={images.length}
                    currentPage={currentPage}
                    setCurrentPage={handlePaginationChange}
                  />
                  <GridContainer
                    ref={containerRef}
                    className="w-full h-full overflow-auto gap-5 p-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
                  >
                    {ImageGrid}
                  </GridContainer>
                </FlexContainer>
              )}
               */}
              <DisplayGenerationHistory />
            </div>
          </TabsContent>
          <TabsContent value="byobImage" className="w-full">
            <FlexContainer className="gap-2 w-full" direction={FlexDirection.COL}>
              <Input type="file" accept="image/*" onChange={handleUserUploadImage} />
              {imageFile !== null ? (
                <FlexContainer
                  className="relative w-full h-[500px] aspect-auto"
                  align={AlignItems.CENTER}
                  justify={JustifyContent.CENTER}
                >
                  <NextImage style={{ objectFit: 'contain' }} src={imageFile.src} fill alt={''} />
                </FlexContainer>
              ) : (
                <NoImageIllustratedMessage
                  className="w-[35%] h-[15%] mx-auto my-auto"
                  title="No image selected"
                  description="Upload your image and it'll appear here!"
                />
              )}
            </FlexContainer>
          </TabsContent>
        </Tabs>
        <DialogFooter>
          <FlexContainer direction={FlexDirection.ROW} className="gap-2">
            <Button
              onClick={handleSelectImage}
              variant="emphasis"
              disabled={handleSelectImageDisabled()}
            >
              <ImagePlus /> Select Image
            </Button>
          </FlexContainer>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default DialogImageGrid
