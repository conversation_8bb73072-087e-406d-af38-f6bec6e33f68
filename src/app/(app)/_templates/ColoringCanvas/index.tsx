'use client'

import { EraserBrush, ClippingGroup } from '@erase2d/fabric'
import * as fabric from 'fabric'
import { Brush, Eraser, PaintBucket, Pencil } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'

import ColorCircle from '../../_component/Button/variants/ColorCircle'
import IconToggleButton from '../../_component/Button/variants/IconToggleButton'
import { Input } from '../../_component/Input'
import { Label } from '../../_component/Label'
import { Slider } from '../../_component/Slider'
import { Switch } from '../../_component/Switch'
import Text from '../../_component/Text'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../_cssComp/FlexContainer'
import GridContainer, { ColSpan, GridItem } from '../../_cssComp/GridContainer'
import { useAuthState } from '../../_state/authState'
import { ImageMetadata } from '../../_types/image'
import { useImagesPagination } from '../../_hooks/useImagesPagination'
import DialogImageGrid from '../DialogImageGrid'

interface Props {
  maxWidth?: number
}

type ConnectedPoints = Map<number, Set<number>>

enum Mode {
  Draw = 'draw',
  Erase = 'erase',
}

enum Tools {
  Brush = 'brush',
  Flood = 'flood',
}

function ColoringCanvas(props: Props) {
  const colorList = [
    {
      colorHex: '#ef4444',
      colorName: 'bg-red-500',
    },
    {
      colorHex: '#f97316',
      colorName: 'bg-orange-500',
    },
    {
      colorHex: '#eab308',
      colorName: 'bg-yellow-500',
    },
    {
      colorHex: '#22c55e',
      colorName: 'bg-green-500',
    },
    {
      colorHex: '#14b8a6',
      colorName: 'bg-teal-500',
    },
    {
      colorHex: '#0ea5e9',
      colorName: 'bg-sky-500',
    },
    {
      colorHex: '#3b82f6',
      colorName: 'bg-blue-500',
    },
    {
      colorHex: '#a855f7',
      colorName: 'bg-purple-500',
    },
    {
      colorHex: '#ec4899',
      colorName: 'bg-pink-500',
    },
  ]
  const userState = useAuthState((state) => state.user)
  const { maxWidth = 1280 } = props
  const canvasEl = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null)
  const [canvasWidth, setCanvasWidth] = useState<number>(800)
  const [canvasHeight, setCanvasHeight] = useState<number>(800)
  const [originalWidth, setOriginalWidth] = useState<number>(0)
  const [originalHeight, setOriginalHeight] = useState<number>(0)
  // const [scaleFactor, setScaleFactor] = useState<number>(1)
  const scaleFactor = useRef(1)
  const [selectedColor, setSelectedColor] = useState(colorList[0])
  const [brushSize, setBrushSize] = useState(10)
  const [selectedTool, setSelectedTool] = useState<Tools>(Tools.Brush)
  const [selectedMode, setSelectedMode] = useState<Mode>(Mode.Draw)
  const [originalImageFile, setOriginalImageFile] = useState<ImageMetadata | null>(null)
  const [imageData, setImageData] = useState<ImageData | null>(null)
  // The higher the stricter
  const [boundaryThreshold, setBoundaryThreshold] = useState<number>(150)
  const [useBoundaryDetection, setUseBoundaryDetection] = useState<boolean>(true)
  // Also use a ref for the threshold so the brush uses the current value.
  const thresholdRef = useRef<number>(boundaryThreshold)

  // Fetch images for the dialog
  const { images } = useImagesPagination({
    pageSize: 10,
    enabled: true,
  })

  useEffect(() => {
    thresholdRef.current = boundaryThreshold
  }, [boundaryThreshold])

  const handleResize = () => {
    if (canvas) {
      handleImageChange(undefined, true)
    }
  }

  useEffect(() => {
    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [canvas, originalImageFile])

  class CustomBrush extends fabric.PencilBrush {
    strokeActive: boolean = true
    connectedPoints: ConnectedPoints = new Map()

    _isPointColorable(x: number, y: number, connectedPoints: ConnectedPoints): boolean {
      return connectedPoints.has(x) && connectedPoints.get(x)!.has(y)
    }

    // createPath(pathData: fabric.TSimplePathData) {
    //   console.log('pathdata',pathData)
    //   const path = super.createPath(pathData)
    //   path.set({
    //     erasable: true,
    //   })
    //   return path
    // }

    _parsePathDataToPoints(pathData: fabric.TSimplePathData): { x: number; y: number }[] {
      const points: { x: number; y: number }[] = []

      for (const segment of pathData) {
        const [command, ...args] = segment

        switch (command) {
          case 'M':
          case 'L':
            // MoveTo / LineTo: [cmd, x, y]
            points.push({ x: args[0]!, y: args[1]! })
            break

          case 'Q':
            // Quadratic curve: [cmd, cpx, cpy, x, y]
            points.push({ x: args[2]!, y: args[3]! })
            break

          case 'C':
            // Cubic curve: [cmd, cp1x, cp1y, cp2x, cp2y, x, y]
            points.push({ x: args[4]!, y: args[5]! })
            break

          case 'Z':
            // ClosePath: no coordinates
            break

          default:
            console.warn(`Unsupported path command: ${command}`)
            break
        }
      }

      return points
    }

    _pointsToPathData(points: { x: number; y: number }[]): fabric.TSimplePathData {
      const pathData: fabric.TSimplePathData = []

      points.forEach((point, index) => {
        if (index === 0) {
          pathData.push(['M', point.x, point.y])
        } else {
          pathData.push(['L', point.x, point.y])
        }
      })

      return pathData
    }

    createPath(pathData: fabric.TSimplePathData) {
      // Convert pathData to points
      const points = this._parsePathDataToPoints(pathData)
      console.log('original', points)
      //TODO: FILTER THE POINTS PROPERLY!!
      console.log('connectedPoints', this.connectedPoints)
      const filteredPoints = points.filter((pt) => {
        console.log(
          this._isPointColorable(Math.floor(pt.x), Math.floor(pt.y), this.connectedPoints),
        )
        return this._isPointColorable(Math.floor(pt.x), Math.floor(pt.y), this.connectedPoints)
      })
      // Convert back to pathData
      const filteredPathData = this._pointsToPathData(filteredPoints)
      console.log('filteredPathData', filteredPathData)
      const path = super.createPath(filteredPathData)
      path.set({ erasable: true })
      return path
    }

    onMouseDown(pointer: fabric.Point, options: { e: fabric.TPointerEvent }) {
      if (isColorable(pointer.x, pointer.y)) {
        super.onMouseDown(pointer, options)
        const startX = Math.floor(pointer.x)
        const startY = Math.floor(pointer.y)
        this.connectedPoints = getConnectedColorablePoints(startX, startY, isColorable)
        console.log(this.connectedPoints)
        this.strokeActive = true
        console.log(this.connectedPoints)
      }
    }

    onMouseMove(pointer: fabric.Point, options: { e: fabric.TPointerEvent }) {
      const startX = Math.floor(pointer.x)
      const startY = Math.floor(pointer.y)
      if (!this._isPointColorable(startX, startY, this.connectedPoints)) {
        this.strokeActive = false
        this._finalizeAndAddPath()
        return
      }
      if (!this.strokeActive && this._isPointColorable(startX, startY, this.connectedPoints)) {
        this.onMouseDown(pointer, options)
      }
      super.onMouseMove(pointer, options)
    }
  }

  const isColorable = (x: number, y: number): boolean => {
    const imgData = imageData
    if (!imgData) return true
    const ix = Math.floor(x)
    const iy = Math.floor(y)
    if (ix < 0 || iy < 0 || ix >= imgData.width || iy >= imgData.height) return false
    const index = (iy * imgData.width + ix) * 4
    const r = imgData.data[index]
    const g = imgData.data[index + 1]
    const b = imgData.data[index + 2]
    const brightness = (r + g + b) / 3
    return brightness > thresholdRef.current
  }

  function getConnectedColorablePoints(
    startX: number,
    startY: number,
    isColorable: (x: number, y: number) => boolean,
  ) {
    const queue = [{ x: startX, y: startY }]
    const connectedPoints: Map<number, Set<number>> = new Map()

    while (queue.length > 0) {
      const point = queue.shift()!
      const { x, y } = point

      if (connectedPoints.has(x) && connectedPoints.get(x)!.has(y)) continue
      if (!isColorable(x, y)) continue
      if (x < 0 || y < 0 || x >= canvasWidth || y >= canvasHeight) continue

      if (!connectedPoints.has(x)) connectedPoints.set(x, new Set())
      connectedPoints.get(x)!.add(y)

      queue.push({ x: x + 1, y })
      queue.push({ x: x - 1, y })
      queue.push({ x, y: y + 1 })
      queue.push({ x, y: y - 1 })
    }

    return connectedPoints
  }

  const cleanupCanvas = (canvas: fabric.Canvas | null) => {
    if (!canvas) return
    canvas.clear()
    canvas.remove(...canvas.getObjects())
    canvas.backgroundImage = undefined
    canvas.backgroundColor = 'white'
    canvas.overlayImage = undefined
    canvas.off()
    canvas.requestRenderAll()
  }

  const removeWhitePixels = async (
    fabricImage: fabric.Image,
    whiteThreshold: number = 220,
  ): Promise<fabric.Image> => {
    const imgElement = fabricImage.getElement()

    // Create a temporary canvas for pixel manipulation
    const tempCanvas = document.createElement('canvas')
    tempCanvas.width = imgElement.width
    tempCanvas.height = imgElement.height

    const tempCtx = tempCanvas.getContext('2d')

    if (!tempCtx) {
      console.error('Could not get 2D rendering context')
      return fabricImage
    }

    tempCtx.drawImage(imgElement, 0, 0, imgElement.width, imgElement.height)

    const imageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height)
    const data = imageData.data

    for (let i = 0; i < data.length; i += 4) {
      if (
        data[i] > whiteThreshold && // Red
        data[i + 1] > whiteThreshold && // Green
        data[i + 2] > whiteThreshold // Blue
      ) {
        data[i + 3] = 0
      }
    }

    // Put modified image data back to canvas
    tempCtx.putImageData(imageData, 0, 0)

    // Create a new image element from the modified canvas
    return new Promise((resolve) => {
      const processedImgElement = new Image()
      processedImgElement.onload = () => {
        // Create a new Fabric.js image from the processed image
        const processedImage = new fabric.FabricImage(processedImgElement, {
          width: fabricImage.width,
          height: fabricImage.height,
          scaleX: fabricImage.scaleX,
          scaleY: fabricImage.scaleY,
          selectable: fabricImage.selectable,
          evented: fabricImage.evented,
        })
        resolve(processedImage)
      }
      processedImgElement.src = tempCanvas.toDataURL('image/png')
    })
  }

  const handleImageChange = async (imageMetadata?: ImageMetadata, resize?: boolean) => {
    let imageMetadataRaw: ImageMetadata | null | undefined
    if (!containerRef.current) return
    if (originalImageFile && resize) {
      imageMetadataRaw = originalImageFile
    } else {
      cleanupCanvas(canvas)
      imageMetadataRaw = imageMetadata
    }
    if (imageMetadataRaw) {
      setOriginalImageFile(imageMetadataRaw)
      if (!canvas) return
      const image = await fabric.FabricImage.fromURL(imageMetadataRaw.src)
      setOriginalWidth(image.width)
      setOriginalHeight(image.height)
      const computedStyle = window.getComputedStyle(containerRef.current)
      const gapValue = parseInt(computedStyle.columnGap || '0', 10)
      const paddingLeft = parseInt(computedStyle.paddingLeft || '0', 10)
      const paddingRight = parseInt(computedStyle.paddingRight || '0', 10)

      // Base container width
      //const containerWidth = containerRef.current.clientWidth

      // Calculate available width accounting for padding and grid gaps
      // In a 12-column grid, there are 11 gaps between columns
      const gridItemCount = 12 // Total columns in your grid
      const gapCount = gridItemCount - 1 // Number of gaps
      const totalGapWidth = gapValue * gapCount

      // // Your canvas is in a colSpan of 9, so calculate the proportion of the container it should use
      // const columnProportion = 9 / 12\
      const containerWidth = containerRef.current.clientWidth
      const columnProportion = 3 / 4

      let availableWidth =
        (containerWidth - totalGapWidth) * columnProportion - paddingLeft - paddingRight

      //if container width is less than or equal to 640px, set columnProportion to 1 (equal to SM in tailwind)
      if (containerWidth <= 725) {
        availableWidth = containerWidth - paddingLeft - paddingRight
        console.log('avail Width', availableWidth)
      }
      const targetWidth = Math.min(availableWidth, maxWidth, image.width)
      const scale = targetWidth / image.width
      const targetHeight = image.height * scale

      setCanvasWidth(targetWidth)
      setCanvasHeight(targetHeight)
      const oldScale = scaleFactor.current
      const newScale = targetWidth / image.width
      rescaleEverything(oldScale, newScale)
      scaleFactor.current = newScale
      canvas.setDimensions({ width: targetWidth, height: targetHeight })

      const processedImage = await removeWhitePixels(image)
      processedImage.scaleToWidth(targetWidth)
      processedImage.set({
        selectable: false,
        evented: false,
      })

      // image.scaleToWidth(canvasWidth)
      image.scaleToWidth(targetWidth)
      image.set({
        selectable: false,
        evented: false,
      })

      canvas.overlayImage = processedImage
      canvas?.renderAll()

      //Image Boundary Detection
      const scaledWidth = image.getScaledWidth()
      const scaledHeight = image.getScaledHeight()
      const tempCanvas = document.createElement('canvas')
      tempCanvas.width = scaledWidth
      tempCanvas.height = scaledHeight

      const tempCtx = tempCanvas.getContext('2d')

      if (tempCtx) {
        const imgElement = image.getElement()

        tempCtx.drawImage(imgElement, 0, 0, scaledWidth, scaledHeight)
        const imgData = tempCtx.getImageData(0, 0, scaledWidth, scaledHeight)
        console.log('Pixel 0,0:', imgData.data[0], imgData.data[1], imgData.data[2])
        setImageData(imgData)
      }
    }
  }

  useEffect(() => {
    if (canvasEl.current) {
      const canvas = new fabric.Canvas(canvasEl.current, {
        width: canvasWidth,
        height: canvasHeight,
      })

      let newBrush: fabric.PencilBrush
      if (useBoundaryDetection) {
        newBrush = new CustomBrush(canvas)
      } else {
        newBrush = new fabric.PencilBrush(canvas)
      }
      newBrush.color = selectedColor.colorHex
      newBrush.width = brushSize
      canvas.freeDrawingBrush = newBrush

      canvas.backgroundColor = `white`
      canvas.renderAll()

      setCanvas(canvas)

      return () => {
        canvas.dispose()
      }
    }
  }, [])

  useEffect(() => {
    if (!canvas) return

    canvas.isDrawingMode = selectedTool === Tools.Brush
    let dispose: (() => void) | null = null

    if (selectedMode === Mode.Draw) {
      if (selectedTool === Tools.Brush) {
        let newBrush: fabric.PencilBrush
        if (useBoundaryDetection) {
          newBrush = new CustomBrush(canvas)
        } else {
          newBrush = new fabric.PencilBrush(canvas)
        }

        newBrush.color = selectedColor.colorHex
        newBrush.width = brushSize
        canvas.freeDrawingBrush = newBrush
      }
      if (selectedTool === Tools.Flood) {
        dispose = canvas.on('mouse:down', (options) => {
          if (options.e) {
            const point = canvas.getScenePoint(options.e)
            floodFill(point.x, point.y, selectedColor.colorHex)
          }
        })
      }
    } else if (selectedMode === Mode.Erase) {
      const eraser = new EraserBrush(canvas)
      eraser.width = brushSize
      canvas.freeDrawingBrush = eraser
      canvas.isDrawingMode = true
    }

    return () => {
      //Updated disposal method: https://github.com/fabricjs/fabric.js/pull/7994
      dispose?.()
    }
  }, [
    canvas,
    selectedTool,
    imageData,
    useBoundaryDetection,
    selectedColor,
    selectedMode,
    brushSize,
  ])

  function rescaleEverything(oldScale: number, newScale: number) {
    if (!canvas) return
    const factor = newScale / oldScale
    canvas.getObjects().forEach((obj) => {
      // adjust object position
      obj.left *= factor
      obj.top *= factor
      // adjust size
      obj.scaleX *= factor
      obj.scaleY *= factor
      obj.setCoords() // recalc bounding boxes
    })
    canvas.renderAll()
  }

  function floodFill(x: number, y: number, selectedColor: string) {
    if (!canvas) return
    const connectedPoints = getConnectedColorablePoints(x, y, isColorable)

    // Optimized rendering using Path instead of individual rectangles
    const pathData: string[] = []
    let currentPath: number[][] = []

    // Sort points for better path optimization
    const sortedPoints = Array.from(connectedPoints.entries())
      .flatMap(([x, ys]) => Array.from(ys).map((y) => ({ x, y })))
      .sort((a, b) => a.y - b.y || a.x - b.x)

    // Generate optimized horizontal lines
    sortedPoints.forEach(({ x, y }) => {
      if (
        currentPath.length === 0 ||
        currentPath[currentPath.length - 1][1] !== y || // New line
        currentPath[currentPath.length - 1][2] + 1 !== x
      ) {
        // Not consecutive
        currentPath.push([x, y, x])
      } else {
        currentPath[currentPath.length - 1][2] = x // Extend horizontal line
      }
    })

    currentPath.forEach(([x1, y, x2]) => {
      pathData.push(`M ${x1} ${y} L ${x2 + 1} ${y} L ${x2 + 1} ${y + 1} L ${x1} ${y + 1} Z`)
    })

    // Create single path element for all filled areas
    const path = new fabric.Path(pathData.join(' '), {
      fill: selectedColor,
      stroke: null,
      selectable: false,
      evented: false,
      objectCaching: true,
      noScaleCache: false,
      erasable: true,
    })

    canvas.add(path)
    canvas.requestRenderAll()
  }

  return (
    <GridContainer
      ref={containerRef}
      className="w-full h-full gap-4 rounded p-8 bg-accent1-lighter"
      columns={1}
    >
      <GridContainer className="w-full h-full bg-accent1-lighter gap-4 grid-cols-1 md:grid-cols-4">
        <GridItem className="bg-main-lightest border-2 border-black rounded h-full col-span-1 order-last sm:order-last md:order-first">
          <FlexContainer direction={FlexDirection.COL} className="h-full">
            <FlexContainer
              direction={FlexDirection.COL}
              className="w-full p-4 gap-2 bg-main-lightest border-b-2"
            >
              <Text variant="emphasis">Choose your Image:</Text>
              <DialogImageGrid
                onImageSelected={handleImageChange}
                isGenerationHistorySelection={userState != null}
                isUserSelection
                images={images}
                pageSize={10}
              />
              {/* <Input className="w-full" type="file" accept="image/*" onChange={handleImageChange} /> */}
            </FlexContainer>
            <FlexContainer className="p-4 border-b-2 gap-2 w-full bg-main-lightest">
              <Text variant="emphasis">Color Palette:</Text>
              <FlexContainer
                className="w-full gap-2"
                align={AlignItems.CENTER}
                justify={JustifyContent.START}
                wrap
              >
                {colorList.map((color, index) => {
                  return (
                    <ColorCircle
                      key={color.colorHex}
                      color={color.colorName}
                      width="8"
                      height="8"
                      active={selectedColor.colorHex === colorList[index].colorHex}
                      onClick={() => setSelectedColor(colorList[index])}
                    />
                  )
                })}
              </FlexContainer>
            </FlexContainer>
            <FlexContainer className="p-4 border-b-2 gap-2 w-full bg-main-lightest">
              <Text variant="emphasis">Draw Mode:</Text>
              <FlexContainer
                className={`w-full gap-4`}
                direction={FlexDirection.ROW}
                align={AlignItems.START}
                justify={JustifyContent.CENTER}
              >
                <IconToggleButton
                  icon={<Pencil />}
                  active={selectedMode === Mode.Draw}
                  onClick={() => setSelectedMode(Mode.Draw)}
                />
                <IconToggleButton
                  icon={<Eraser />}
                  active={selectedMode === Mode.Erase}
                  onClick={() => setSelectedMode(Mode.Erase)}
                />
              </FlexContainer>
            </FlexContainer>
            <FlexContainer className="p-4 border-b-2 gap-2 w-full bg-main-lightest">
              <Text variant="emphasis">Brush Mode:</Text>
              <FlexContainer
                className={`w-full gap-4`}
                direction={FlexDirection.ROW}
                align={AlignItems.START}
                justify={JustifyContent.CENTER}
              >
                <IconToggleButton
                  icon={<Brush />}
                  active={selectedTool === Tools.Brush}
                  onClick={() => setSelectedTool(Tools.Brush)}
                />
                <IconToggleButton
                  icon={<PaintBucket />}
                  active={selectedTool === Tools.Flood}
                  onClick={() => setSelectedTool(Tools.Flood)}
                />
              </FlexContainer>
              <Text>Brush Size:</Text>
              <Slider
                value={[brushSize]}
                max={20}
                step={1}
                onValueChange={(e) => setBrushSize(e[0])}
              />
            </FlexContainer>
            <FlexContainer
              direction={FlexDirection.COL}
              className="p-4 border-b-2 gap-2 w-full bg-main-lightest"
            >
              <Text variant="emphasis">Brush Type:</Text>
              Add crayons / pen etc...
            </FlexContainer>
            <FlexContainer
              direction={FlexDirection.COL}
              className="p-4 border-b-2 gap-2 w-full bg-main-lightest"
            >
              <Text variant="emphasis">Settings:</Text>
              <FlexContainer
                direction={FlexDirection.ROW}
                align={AlignItems.CENTER}
                className="gap-2"
              >
                <Switch
                  id="boundary-mode"
                  checked={useBoundaryDetection}
                  onCheckedChange={() => setUseBoundaryDetection(!useBoundaryDetection)}
                />
                <Label htmlFor="boundary-mode">Smart Coloring</Label>
              </FlexContainer>
            </FlexContainer>
          </FlexContainer>
        </GridItem>
        <GridItem className="col-span-1 md:col-span-3 min-h-[50vh] order-first sm:order-first md:order-last">
          <FlexContainer
            className="w-full h-full relative"
            direction={FlexDirection.COL}
            align={AlignItems.CENTER}
            justify={JustifyContent.CENTER}
          >
            <canvas id="canvas" ref={canvasEl} />
          </FlexContainer>
        </GridItem>
      </GridContainer>
    </GridContainer>
  )
}

export default ColoringCanvas
