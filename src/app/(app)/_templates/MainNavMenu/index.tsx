'use client'

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>em, <PERSON><PERSON>, Rss } from 'lucide-react'
import NextImage from 'next/image'
import Link from 'next/link'
import React, { useState } from 'react'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDes<PERSON>,
  DrawerFooter,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from '@/app/(app)/_component/Drawer'
import { useMediaQuery } from '@/app/(app)/_hooks/MediaQuery'
import { cn } from '@/lib/utils'
import { Spacing } from '@/utilities/local/css'
import LoginSignUpSection from './LoginSignUpSection'
import { Button } from '../../_component/Button'
import ListButton from '../../_component/Button/variants/ListButton'
import NavigationMenu, { NavigationMenuLinkItem } from '../../_component/NavigationMenuV2'
import { ScrollProgress } from '../../_component/ScrollProgress'
import Text from '../../_component/Text'
import Title from '../../_component/Title'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../_cssComp/FlexContainer'
import GridContainer, {
  ColSpan,
  GridContentAlignment,
  GridItem,
} from '../../_cssComp/GridContainer'

function MainNavMenu() {
  const HOST_URL = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'
  const isDesktop = useMediaQuery('(min-width: 900px)')
  return (
    <nav className="relative">
      <GridContainer
        columns={3}
        className={cn(
          `w-full ${Spacing.ContentPadding} border-b-2`,
          `${isDesktop ? 'grid-cols-5' : 'grid-cols-2'}`,
        )}
        contentAlign={GridContentAlignment.CENTER}
      >
        <GridItem className="col-span-1">
          <Link href={HOST_URL}>
            <FlexContainer className={`w-full h-full gap-2`} wrap={false} align={AlignItems.CENTER}>
              <NextImage
                src="/media/logo/logo_v4.svg"
                width={50}
                height={50}
                alt="ColorAria logo"
              />
              <Title>ColorAria</Title>
            </FlexContainer>
          </Link>
        </GridItem>
        {isDesktop ? (
          <>
            <GridItem className="col-span-3 justify-self-center self-center">
              <NavigationMenu>
                <NavigationMenuLinkItem link="pricing" label="Pricing" icon={<Gem />} />
                <NavigationMenuLinkItem link="blog" label="Blog" icon={<Rss />} />
                <NavigationMenuLinkItem
                  link="coloring"
                  label="Online Coloring"
                  icon={<Palette />}
                />
              </NavigationMenu>
            </GridItem>
            <GridItem className="col-span-1 justify-self-end self-center">
              <LoginSignUpSection />
            </GridItem>
          </>
        ) : (
          <GridItem colSpan={ColSpan.SPAN_1} className="justify-self-end self-center">
            <Drawer direction="right">
              <DrawerTrigger asChild>
                <Button>
                  <AlignJustify />
                </Button>
              </DrawerTrigger>
              <DrawerContent className="z-401">
                <DrawerHeader>
                  <DrawerTitle>ColorAria</DrawerTitle>
                  <DrawerDescription>Welcome to ColorAria!</DrawerDescription>
                </DrawerHeader>
                <FlexContainer
                  direction={FlexDirection.COL}
                  justify={JustifyContent.CENTER}
                  align={AlignItems.CENTER}
                  className="w-full gap-4"
                >
                  <LoginSignUpSection className="justify-center align-center" isCenter />
                  <FlexContainer direction={FlexDirection.COL} className="w-full">
                    <Link href={`${HOST_URL}/pricing`} className="w-full border-t-2 border-black">
                      <ListButton className="w-full">
                        <Text variant="emphasis" size="base">
                          <FlexContainer className="gap-2" align={AlignItems.CENTER}>
                            <Gem /> Pricing
                          </FlexContainer>
                        </Text>
                      </ListButton>
                    </Link>
                    <Link href={`${HOST_URL}/blog`} className="w-full border-t-2 border-black">
                      <ListButton className="w-full">
                        <Text variant="emphasis" size="base">
                          <FlexContainer className="gap-2" align={AlignItems.CENTER}>
                            <Rss /> Blog
                          </FlexContainer>
                        </Text>
                      </ListButton>
                    </Link>
                    <Link href={`${HOST_URL}/coloring`} className="w-full border-y-2 border-black">
                      <ListButton className="w-full">
                        <Text variant="emphasis" size="base">
                          <FlexContainer className="gap-2" align={AlignItems.CENTER}>
                            <Palette /> Online Coloring{' '}
                          </FlexContainer>
                        </Text>
                      </ListButton>
                    </Link>
                  </FlexContainer>
                </FlexContainer>
                <DrawerFooter>
                  <DrawerClose asChild></DrawerClose>
                </DrawerFooter>
              </DrawerContent>
            </Drawer>
          </GridItem>
        )}
        <ScrollProgress className="h-1 w-full" />
      </GridContainer>
    </nav>
  )
}

export default MainNavMenu
