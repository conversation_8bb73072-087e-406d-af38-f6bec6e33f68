'use client'
import { UseMutationResult } from '@tanstack/react-query'
import { ChevronDown, Crown, Palette, Text as TextIcon } from 'lucide-react'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { Badge } from '@/app/(app)/_component/Badge'
import { Input } from '@/app/(app)/_component/Input'
import { Slider } from '@/app/(app)/_component/Slider'
import { Switch } from '@/app/(app)/_component/Switch'
import Text from '@/app/(app)/_component/Text'
import FlexContainer, { AlignItems, FlexDirection } from '@/app/(app)/_cssComp/FlexContainer'
import { ApiResponse } from '@/app/(app)/_localApi/util'
import { useAuthState } from '@/app/(app)/_state/authState'
import { ImageGenerationResponseT2I } from '@/app/(app)/api/image-generation/text-to-image/route'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '../../../_component/Accordion'
import { Button } from '../../../_component/Button'
import Card from '../../../_component/Card/CardApplication'
import { Label } from '../../../_component/Label'
import { RadioGroup, RadioGroupItem } from '../../../_component/RadioGroup'
import { Textarea } from '../../../_component/Textarea'
import Title, { HeaderLevel } from '../../../_component/Title'
import PromptGuidance from '../PromptGuidance'

interface Props {
  generateImage: UseMutationResult<
    ApiResponse<ImageGenerationResponseT2I>,
    Error,
    { prompt: string; aspectRatio: string; numberOfImages: number; highQuality: boolean },
    unknown
  >
}

const CardHeader = () => {
  return (
    <div className="w-full h-full flex flex-row gap-2 items-center">
      <TextIcon />
      <Title className="text-dark" level={HeaderLevel.H4}>
        Text to Coloring Page
      </Title>
    </div>
  )
}

const mainLink = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'

function TextImageGeneratorCard(props: Props) {
  const { generateImage } = props
  const [prompt, setPrompt] = useState('')
  const [disableGenerate, setDisableGenerate] = useState(true)
  const [aspectRatio, setAspectRatio] = useState('1:1')
  const [batchMode, setBatchMode] = useState(1)
  const [openGuidedPrompt, setOpenGuidedPrompt] = useState(false)
  const [highQuality, setHighQuality] = useState(false)
  const authUser = useAuthState()

  const onGenerateImage = async () => {
    const result = await generateImage.mutateAsync({
      prompt: prompt,
      aspectRatio: aspectRatio,
      numberOfImages: batchMode,
      highQuality: highQuality,
    })
  }

  useEffect(() => {
    if (prompt.trim() !== '') {
      setDisableGenerate(false)
    } else {
      setDisableGenerate(true)
    }
  }, [prompt])

  return (
    <Card title={<CardHeader />}>
      <div className="w-full grid grid-cols-12 gap-4">
        <div className="flex flex-col w-full col-span-12 gap-3">
          <div className="flex flex-row justify-between items-center">
            <Text variant="emphasis">Add your prompts:</Text>
            <PromptGuidance
              open={openGuidedPrompt}
              setOpen={setOpenGuidedPrompt}
              onSubmit={setPrompt}
            />
          </div>
          <FlexContainer direction={FlexDirection.COL} className="gap-2">
            <Textarea
              value={prompt}
              rows={4}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Your prompts should be as descriptive as possible for the best result. Consult our guide if you need help."
              className="w-full"
            />
            <Text size="sm" variant="description">
              Need some ideas? Take a look at our{' '}
              <Link
                className="text-accent2 hover:text-accent2-lighter underline"
                href={`${mainLink}/dashboard/image-library`}
              >
                image library~
              </Link>
            </Text>
          </FlexContainer>
        </div>
        <FlexContainer className="flex flex-col w-full col-span-12 gap-8 mt-5">
          <Accordion className="w-full" type="single" collapsible>
            <AccordionItem className="max-w-full" value="item-1">
              <AccordionTrigger>Image Aspect Ratio</AccordionTrigger>
              <AccordionContent>
                <div className="flex flex-col gap-4">
                  <Label>Pick an Aspect Ratio the image will be generated in.</Label>
                  <RadioGroup
                    defaultValue="square"
                    value={aspectRatio}
                    onValueChange={(e) => setAspectRatio(e)}
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="1:1" id="square" />
                      <Label htmlFor="square">1:1 (Square)</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="2:3" id="portrait" />
                      <Label htmlFor="portrait">2:3 (Portrait)</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="3:2" id="landscape" />
                      <Label htmlFor="landscape">3:2 (Landscape)</Label>
                    </div>
                  </RadioGroup>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
          <FlexContainer direction={FlexDirection.COL} className="gap-4 w-full">
            <FlexContainer
              direction={FlexDirection.ROW}
              className="gap-2 w-full"
              align={AlignItems.CENTER}
            >
              <Label>
                <Text variant="emphasis">Batch Mode:</Text>
              </Label>
              {authUser.user?.role.toLowerCase() === 'free' ? (
                <Badge variant="destructive">
                  <Crown />
                  Premium Only
                </Badge>
              ) : (
                <></>
              )}
            </FlexContainer>
            <Slider
              value={[batchMode]}
              max={10}
              step={1}
              onValueChange={(e) => setBatchMode(e[0])}
            />
            <Input
              type="number"
              value={batchMode}
              onChange={(e) => setBatchMode(e.target.value !== '' ? parseInt(e.target.value) : 0)}
            />
            <Text size="sm" variant="description">
              {`We cannot guarantee the first generated image is perfect, therefore it's recommended
                to generate more than 1.`}
            </Text>
          </FlexContainer>
        </FlexContainer>
        <FlexContainer
          direction={FlexDirection.ROW}
          className="gap-4 w-full col-span-12"
          align={AlignItems.CENTER}
        >
          <Switch checked={highQuality} onCheckedChange={setHighQuality} />{' '}
          <Text variant="emphasis">High Native Quality</Text>
        </FlexContainer>
        <Button
          className="mt-5 w-full col-span-12"
          variant="emphasis"
          disabled={disableGenerate}
          onClick={onGenerateImage}
        >
          <Palette />
          Generate Image
        </Button>
      </div>
    </Card>
  )
}

export default TextImageGeneratorCard
