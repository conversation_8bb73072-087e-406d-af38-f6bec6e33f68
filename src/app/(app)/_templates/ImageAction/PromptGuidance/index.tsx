import { <PERSON>, WandSparkles } from 'lucide-react'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { Badge } from '@/app/(app)/_component/Badge'
import { Button } from '@/app/(app)/_component/Button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/app/(app)/_component/Dialog'
import { Label } from '@/app/(app)/_component/Label'
import Text from '@/app/(app)/_component/Text'
import { Textarea } from '@/app/(app)/_component/Textarea'
import FlexContainer, { AlignItems, FlexDirection } from '@/app/(app)/_cssComp/FlexContainer'
import GridContainer from '@/app/(app)/_cssComp/GridContainer'
import { allValuesInObjectEmpty } from '@/utilities/local/object'

interface Props {
  open: boolean
  setOpen: Dispatch<SetStateAction<boolean>>
  onSubmit?: Dispatch<SetStateAction<string>>
}

function PromptGuidance(props: Props) {
  const { open, setOpen, onSubmit } = props
  const [form, setForm] = useState({
    subject: '',
    pose: '',
    setting: '',
    composition: '',
  })
  const [resultingPrompt, setResultingPrompt] = useState('')
  const setFormValues = (value: string | null, category: string) => {
    setForm({
      ...form,
      [category]: value,
    })
  }

  useEffect(() => {
    const result = `${form.subject} ${form.pose} ${form.setting} ${form.composition}`
    if (result.trim() === '') {
      setResultingPrompt("There's nothing yet!~")
    } else {
      setResultingPrompt(`${form.subject} ${form.pose} ${form.setting} ${form.composition}`)
    }
  }, [form])

  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit(resultingPrompt)
    }
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size="sm" variant="positive" round="round">
          Guide?
        </Button>
      </DialogTrigger>
      <DialogContent className="w-3/4 z-501 max-h-[90%]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <WandSparkles /> Prompt Guidance
          </DialogTitle>
          <DialogDescription>
            Use this tool to effortlessly craft the perfect prompt for your ideal image. <br />
            All fields are optional — explore and create at your own pace!
          </DialogDescription>
        </DialogHeader>
        <GridContainer className="grid-cols-1 gap-8 max-h-full px-4 overflow-y-auto">
          <FlexContainer direction={FlexDirection.COL} className="gap-2">
            <Label>Subject:</Label>
            <Textarea
              className="h-auto"
              rows={1}
              placeholder={
                'Recommended to add a descriptor on top of your subject. (Eg: A Young woman, A Magician wearing a crooked hat etc)'
              }
              value={form.subject}
              onChange={(e) => setFormValues(e.target.value, 'subject')}
            />
            <FlexContainer
              direction={FlexDirection.ROW}
              align={AlignItems.CENTER}
              className="gap-2"
              wrap
            >
              <Text variant="description" size="xs">
                Suggested examples:
              </Text>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'subject')}
              >
                An [Animal / Fantasy Creature]
              </Badge>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'subject')}
              >
                A [Toy]
              </Badge>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'subject')}
              >
                A Group of [Characters]
              </Badge>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'subject')}
              >
                [Buildings]
              </Badge>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'subject')}
              >
                [Vehicles]
              </Badge>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'subject')}
              >
                [Food]
              </Badge>
            </FlexContainer>
          </FlexContainer>
          <FlexContainer direction={FlexDirection.COL} className="gap-2">
            <Label>Pose:</Label>
            <Textarea
              value={form.pose}
              rows={1}
              onChange={(e) => setFormValues(e.target.value, 'pose')}
            />
            <FlexContainer
              direction={FlexDirection.ROW}
              align={AlignItems.CENTER}
              className="gap-2"
              wrap
            >
              <Text variant="description" size="xs">
                Suggested examples:
              </Text>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'pose')}
              >
                Standing ready for action
              </Badge>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'pose')}
              >
                Soaring through the sky
              </Badge>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'pose')}
              >
                Front-facing view
              </Badge>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'pose')}
              >
                Side-facing view
              </Badge>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'pose')}
              >
                Standing on two legs
              </Badge>
            </FlexContainer>
          </FlexContainer>
          <FlexContainer direction={FlexDirection.COL} className="gap-2">
            <Label>Setting:</Label>
            <Textarea
              value={form.setting}
              rows={1}
              onChange={(e) => setFormValues(e.target.value, 'setting')}
            />
            <FlexContainer
              direction={FlexDirection.ROW}
              align={AlignItems.CENTER}
              className="gap-2"
              wrap
            >
              <Text variant="description" size="xs">
                Suggested examples:
              </Text>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'setting')}
              >
                In a grassy field with flowers
              </Badge>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'setting')}
              >
                Above a cityscape at sunset
              </Badge>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'setting')}
              >
                In a high-tech laboratory
              </Badge>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'setting')}
              >
                Clear blue-sky with fluffy clouds
              </Badge>
            </FlexContainer>
          </FlexContainer>
          <FlexContainer direction={FlexDirection.COL} className="gap-2">
            <Label>Composition:</Label>
            <Textarea
              value={form.composition}
              rows={1}
              onChange={(e) => setFormValues(e.target.value, 'composition')}
            />
            <FlexContainer
              direction={FlexDirection.ROW}
              align={AlignItems.CENTER}
              className="gap-2"
              wrap
            >
              <Text variant="description" size="xs">
                Suggested examples:
              </Text>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'composition')}
              >
                Dynamic, with [subject] in [position / action] and setting in [position /
                background]
              </Badge>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'composition')}
              >
                Detailed, with [subject] surrounded by [details]
              </Badge>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'composition')}
              >
                On an empty white background
              </Badge>
              <Badge
                type="button"
                onClick={(e) => setFormValues(e.currentTarget.textContent, 'composition')}
              >
                That is centered with [details]
              </Badge>
            </FlexContainer>
          </FlexContainer>
        </GridContainer>
        <FlexContainer direction={FlexDirection.COL} className="gap-2 max-w-full" wrap>
          <Label>Resulting Prompt:</Label>
          <Text className="break-all whitespace-normal max-w-full">{resultingPrompt}</Text>
        </FlexContainer>
        <DialogFooter>
          <Button
            onClick={handleSubmit}
            disabled={allValuesInObjectEmpty(form)}
            className="w-full"
            variant="love"
          >
            <Heart /> I like this Prompt!
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default PromptGuidance
