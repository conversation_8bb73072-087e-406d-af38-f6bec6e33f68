import { XIcon } from 'lucide-react'
import NextImage from 'next/image'

import { But<PERSON> } from '@/app/(app)/_component/Button'
import FlexContainer, { AlignItems, JustifyContent } from '@/app/(app)/_cssComp/FlexContainer'
import { ImageMetadata } from '@/app/(app)/_state/imageState'
import { cn } from '@/lib/utils'

interface Props {
  image: ImageMetadata
  className?: string
  onClose: () => void
}

function ImageLightBox(props: Props) {
  const { image, className, onClose } = props

  return (
    <FlexContainer
      className={cn(`w-screen h-screen fixed inset-0 bg-black/75 z-402`, className)}
      justify={JustifyContent.CENTER}
      align={AlignItems.CENTER}
      onClick={onClose}
    >
      <div className="relative max-w-full max-h-full p-4">
        <NextImage
          src={image.src}
          alt={'prompt image'}
          width={image.width || 800}
          height={image.height || 600}
          className="object-contain w-full h-auto max-h-[80vh] rounded-lg cursor-pointer"
          priority
        />
        <Button
          onClick={onClose}
          variant="negative"
          size="icon"
          className="absolute top-8 right-8 rounded-full"
        >
          <XIcon />
        </Button>
      </div>
    </FlexContainer>
  )
}

export default ImageLightBox
