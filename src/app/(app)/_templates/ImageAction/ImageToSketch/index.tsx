'use client'
import { UseMutationResult } from '@tanstack/react-query'
import { ChevronDown } from 'lucide-react'
import { useEffect, useState } from 'react'
import { v4 } from 'uuid'
import UploadBox from '@/app/(app)/_component/UploadBox'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '@/app/(app)/_cssComp/FlexContainer'

import { ImageMetadata } from '@/app/(app)/_state/imageState'
import { ActionType } from '@/utilities/local/enums'
import { Button } from '../../../_component/Button'
import Card from '../../../_component/Card/CardApplication'
import { Label } from '../../../_component/Label'
import Title, { HeaderLevel } from '../../../_component/Title'

interface Props {
  generateImage: UseMutationResult<
    string,
    Error,
    {
      imageBase64: string
    },
    void
  >
  extImage: ImageMetadata | null
}

const CardHeader = () => {
  return (
    <div className="w-full h-full flex flex-row gap-2 content-center">
      <ChevronDown className="mt-1" />
      <Title className="text-dark" level={HeaderLevel.H4}>
        Convert Image to Sketch
      </Title>
    </div>
  )
}

function ImageToSketch(props: Props) {
  const { generateImage, extImage } = props
  const [selectedImage, setSelectedImage] = useState<ImageMetadata>({
    id: v4(),
    src: extImage?.src || '',
    width: 50,
    height: 50,
    actionType: ActionType.DRAWING_TO_SKETCH,
  })

  useEffect(() => {
    if (extImage) {
      setSelectedImage({
        id: v4(),
        src: extImage.src,
        width: extImage.width,
        height: extImage.height,
        actionType: ActionType.DRAWING_TO_SKETCH,
      })
    }
  }, [extImage])

  const onGenerateImage = async () => {
    const result = await generateImage.mutateAsync({ imageBase64: selectedImage.src })
    console.log(result)
  }

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()

      reader.onload = () => {
        if (reader.result) {
          const img = new Image()
          img.onload = () => {
            setSelectedImage({
              id: v4(),
              src: reader.result as string,
              width: img.width,
              height: img.height,
              actionType: ActionType.DRAWING_TO_SKETCH,
            })
          }
          img.src = reader.result as string
        }
      }

      reader.readAsDataURL(file)
    }
  }

  return (
    <Card title={<CardHeader />}>
      <div className="w-full grid grid-cols-12">
        <div className="flex flex-col w-full col-span-12 gap-3">
          <div className="flex items-center justify-start gap-3 flex-row sm:flex-col sm:items-start">
            <Label className="">Upload an Image to the timeline:</Label>
            <FlexContainer
              direction={FlexDirection.ROW}
              justify={JustifyContent.BETWEEN}
              align={AlignItems.CENTER}
              className={'gap-2 w-full'}
            >
              <UploadBox existingImage={selectedImage.src} handleChange={handleImageChange} />
            </FlexContainer>
          </div>
        </div>
        <Button className="mt-5 w-full col-span-12" onClick={onGenerateImage}>
          Convert Image to Sketch
        </Button>
      </div>
    </Card>
  )
}

export default ImageToSketch
