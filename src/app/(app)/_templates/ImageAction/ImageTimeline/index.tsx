'use client'

import { Label } from '@radix-ui/react-label'
import { ChevronUp, ChevronDown } from 'lucide-react'
import Image from 'next/image'
import { Dispatch, Suspense, useEffect, useRef, useState } from 'react'
import RingLoader from 'react-spinners/RingLoader'
import { ImageMetadata } from '@/app/(app)/_state/imageState'
import { Button } from '../../../_component/Button'
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '../../../_component/Resizeable'

interface Props {
  imageTimeline: ImageMetadata[]
  setImageTimeline: Dispatch<ImageMetadata[]>
  selectedImage: ImageMetadata | null
  setSelectedImage: (index: number) => void
  loading: boolean
}

interface ImageSectionProps {
  loading: boolean
  selectedImage: ImageMetadata | null
}

const ImageSection: React.FC<ImageSectionProps> = ({ loading, selectedImage }) => {
  if (loading) {
    return <RingLoader />
  } else if (selectedImage == null) {
    return <>Nothing to see</>
  }
  return (
    <Image
      style={{
        objectFit: 'contain',
        maxWidth: '100%', // Constrain width to the parent
        maxHeight: '100%',
      }} // Constrain height to the parent   }}
      src={selectedImage.src}
      width={selectedImage.width || 1} // Use default values to avoid errors
      height={selectedImage.height || 1}
      alt={''}
    />
  )
}

function ImageTimeline(props: Props) {
  const { imageTimeline, setImageTimeline, selectedImage, setSelectedImage, loading } = props
  const [imageDimension, setImageDimension] = useState({ width: 0, height: 0 })

  const scrollContainerRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    if (imageTimeline) {
      if (scrollContainerRef.current) {
        scrollContainerRef.current.scrollBy({ top: 1000, behavior: 'smooth' })
      }
    }
  }, [imageTimeline])

  const handleScrollUp = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ top: -100, behavior: 'smooth' })
    }
  }

  // Function to handle scrolling down
  const handleScrollDown = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ top: 100, behavior: 'smooth' })
    }
  }

  return (
    <div className="h-full overflow-hidden">
      <ResizablePanelGroup direction="horizontal">
        <ResizablePanel defaultSize={20}>
          <div className="flex flex-col items-center h-full w-full">
            <div className="basis-[10%]">
              <Button variant="secondary" className="border-radius" onClick={handleScrollUp}>
                <ChevronUp />
              </Button>
            </div>

            <div
              className={`overflow-y-auto h-full w-full flex flex-col items-center scrollbar-hide grow basis-[80%] `}
              ref={scrollContainerRef}
            >
              {imageTimeline.length > 0 ? (
                imageTimeline.map((image, index) => (
                  <Suspense key={index} fallback={<p>Loading</p>}>
                    <Image
                      className="mt-4 mb-4 rounded-md"
                      alt={'cool image'}
                      src={image.src}
                      width={168} // Adjust as needed
                      height={168} // Adjust as needed
                      onClick={() => setSelectedImage(index)}
                    />
                  </Suspense>
                ))
              ) : (
                <Label>Nothing!</Label>
              )}
            </div>

            <div className="mt-5 basis-[10%]">
              <Button variant="secondary" className="border-radius" onClick={handleScrollDown}>
                <ChevronDown />
              </Button>
            </div>
          </div>
        </ResizablePanel>
        <ResizableHandle />
        <ResizablePanel defaultSize={80} className="flex items-center justify-center">
          <div className="h-full w-full flex items-center justify-center">
            <ImageSection loading={loading} selectedImage={selectedImage} />
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  )
}

export default ImageTimeline
