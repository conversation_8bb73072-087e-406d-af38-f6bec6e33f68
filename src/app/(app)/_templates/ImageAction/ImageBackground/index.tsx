'use client'

import { UseMutationResult } from '@tanstack/react-query'
import { ChevronDown, ImageMinus } from 'lucide-react'
import NextImage from 'next/image'
import { useEffect, useState } from 'react'
import { v4 } from 'uuid'
import Text from '@/app/(app)/_component/Text'
import UploadBox from '@/app/(app)/_component/UploadBox'
import { ApiResponse } from '@/app/(app)/_localApi/util'
import { ImageMetadata } from '@/app/(app)/_state/imageState'
import { RemoveBackgroundResponse } from '@/app/(app)/api/image-generation/remove-background/route'
import { ActionType } from '@/utilities/local/enums'
import { Button } from '../../../_component/Button'
import Card from '../../../_component/Card/CardApplication'
import { Label } from '../../../_component/Label'
import Title, { HeaderLevel } from '../../../_component/Title'

interface Props {
  extImage: ImageMetadata | null
  generateImage: UseMutationResult<
    ApiResponse<RemoveBackgroundResponse>,
    Error,
    {
      src: string
    },
    void
  >
}

const CardHeader = () => {
  return (
    <div className="w-full h-full flex flex-row gap-2 content-center">
      <ImageMinus />
      <Title className="text-dark" level={HeaderLevel.H4}>
        AI Image Background Remover
      </Title>
    </div>
  )
}

function ImageBackgroundRemover(props: Props) {
  const { extImage, generateImage } = props
  const [selectedImage, setSelectedImage] = useState<ImageMetadata>({
    id: v4(),
    src: extImage?.src || '',
    width: 50,
    height: 50,
    actionType: ActionType.AI_BACKGROUND_REMOVER,
  })

  useEffect(() => {
    if (extImage) {
      setSelectedImage({
        id: v4(),
        src: extImage.src,
        width: extImage.width,
        height: extImage.height,
        actionType: ActionType.AI_BACKGROUND_REMOVER,
      })
    }
  }, [extImage])

  const onGenerateImage = async () => {
    const result = await generateImage.mutateAsync({ src: selectedImage.src })
    console.log(result)
  }

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()

      reader.onload = () => {
        if (reader.result) {
          const img = new Image()
          img.onload = () => {
            setSelectedImage({
              id: v4(),
              src: reader.result as string,
              width: img.width,
              height: img.height,
              actionType: ActionType.AI_BACKGROUND_REMOVER,
            })
          }
          img.src = reader.result as string
        }
      }

      reader.readAsDataURL(file)
    }
  }

  return (
    <Card title={<CardHeader />}>
      <div className="w-full grid grid-cols-12">
        <div className="flex flex-col w-full col-span-12 gap-3">
          <div className="flex items-center justify-start gap-3 flex-row sm:flex-col sm:items-start">
            <Text variant="emphasis">Upload an Image:</Text>
            <UploadBox existingImage={selectedImage.src} handleChange={handleImageChange} />
          </div>
        </div>
        <Button
          className="mt-5 w-full col-span-12"
          variant="emphasis"
          disabled={!selectedImage.src}
          onClick={onGenerateImage}
        >
          <ImageMinus /> Remove Background
        </Button>
      </div>
    </Card>
  )
}

export default ImageBackgroundRemover
