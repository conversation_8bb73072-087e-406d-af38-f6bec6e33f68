'use client'
import { UseMutationResult } from '@tanstack/react-query'
import { Crown, Palette } from 'lucide-react'
import Link from 'next/link'
import { useEffect, useState } from 'react'

import { v4 } from 'uuid'
import { Badge } from '@/app/(app)/_component/Badge'
import { Input } from '@/app/(app)/_component/Input'
import { Slider } from '@/app/(app)/_component/Slider'
import Text from '@/app/(app)/_component/Text'
import UploadBox from '@/app/(app)/_component/UploadBox'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '@/app/(app)/_cssComp/FlexContainer'
import { useAuthState } from '@/app/(app)/_state/authState'
import { ImageMetadata } from '@/app/(app)/_state/imageState'
import { ActionType } from '@/utilities/local/enums'
import { Button } from '../../../_component/Button'
import Card from '../../../_component/Card/CardApplication'
import { Label } from '../../../_component/Label'
import { Textarea } from '../../../_component/Textarea'
import Title, { HeaderLevel } from '../../../_component/Title'
import { FetchImageToImage } from '../../../_localApi/imageGeneration'

const mainLink = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'

interface Props {
  generateImage: UseMutationResult<
    FetchImageToImage,
    Error,
    {
      prompt: string
      imageBase64: string
    },
    void
  >
  extImage: ImageMetadata | null
}

const CardHeader = () => {
  return (
    <div className="w-full h-full flex flex-row gap-2 content-center">
      <Title className="text-dark" level={HeaderLevel.H4}>
        ✏️ Scribbles to Coloring Image
      </Title>
    </div>
  )
}

function ScribbleToColoringImage(props: Props) {
  const { generateImage, extImage } = props
  const [prompt, setPrompt] = useState('')
  const [batchMode, setBatchMode] = useState(1)
  const [selectedImage, setSelectedImage] = useState<ImageMetadata>({
    id: v4(),
    src: extImage?.src || '',
    width: 50,
    height: 50,
    actionType: ActionType.SCRIBBLE_TO_COLOR,
  })
  const authUser = useAuthState()

  useEffect(() => {
    if (extImage) {
      setSelectedImage({
        id: v4(),
        src: extImage.src,
        width: extImage.width,
        height: extImage.height,
        actionType: ActionType.AI_BACKGROUND_REMOVER,
      })
    }
  }, [extImage])

  const onGenerateImage = async () => {
    const result = await generateImage.mutateAsync({
      prompt: prompt,
      imageBase64: selectedImage.src,
    })
  }

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()

      reader.onload = () => {
        if (reader.result) {
          const img = new Image()
          img.onload = () => {
            setSelectedImage({
              id: v4(),
              src: reader.result as string,
              width: img.width,
              height: img.height,
              actionType: ActionType.SCRIBBLE_TO_COLOR,
            })
          }
          img.src = reader.result as string
        }
      }

      reader.readAsDataURL(file)
    }
  }

  return (
    <Card title={<CardHeader />}>
      <div className="w-full grid grid-cols-12">
        <div className="flex flex-col w-full col-span-12 gap-3">
          <div className="flex items-center justify-start gap-3 flex-row sm:flex-col sm:items-start w-full">
            <FlexContainer
              direction={FlexDirection.ROW}
              justify={JustifyContent.CENTER}
              align={AlignItems.CENTER}
              className="gap-2"
            >
              <Text variant="emphasis">Upload an Image:</Text>
              <Label>
                <Link
                  className="text-accent5 hover:text-accent5-lighter underline"
                  href={`${mainLink}/dashboard/image-library`}
                >
                  (Need inspiration?)
                </Link>
              </Label>
            </FlexContainer>
            <FlexContainer direction={FlexDirection.ROW} className="w-full">
              <UploadBox
                existingImage={selectedImage.src}
                description="Landscapes, animals, nature, etc are supported"
                handleChange={handleImageChange}
              />
            </FlexContainer>
          </div>
          <div className="flex flex-row justify-between items-center">
            <Text variant="emphasis">Add your prompts:</Text>
            <Button size="sm" variant="positive" round="round">
              Guide?
            </Button>
          </div>
          <Textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            rows={4}
            placeholder="Add your prompts"
            className="w-full"
          />
          <FlexContainer direction={FlexDirection.COL} className="gap-4 w-full mt-2">
            <FlexContainer
              direction={FlexDirection.ROW}
              className="gap-2 w-full"
              align={AlignItems.CENTER}
            >
              <Label>
                <Text variant="emphasis">Batch Mode:</Text>
              </Label>
              {authUser.user?.role.toLowerCase() === 'free' ? (
                <Badge variant="destructive">
                  <Crown />
                  Premium Only
                </Badge>
              ) : (
                <></>
              )}
            </FlexContainer>
            <Slider
              value={[batchMode]}
              max={10}
              step={1}
              onValueChange={(e) => setBatchMode(e[0])}
            />
            <Input
              type="number"
              value={batchMode}
              onChange={(e) => setBatchMode(e.target.value !== '' ? parseInt(e.target.value) : 0)}
            />
            <Text size="sm" variant="description">
              {`We cannot guarantee the first generated image is perfect, therefore it's recommended
                to generate more than 1.`}
            </Text>
          </FlexContainer>
        </div>
        <Button className="mt-5 w-full col-span-12" variant="emphasis" onClick={onGenerateImage}>
          <Palette />
          Generate Image
        </Button>
      </div>
    </Card>
  )
}

export default ScribbleToColoringImage
