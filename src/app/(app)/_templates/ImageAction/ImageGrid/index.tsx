'use client'

import { use<PERSON><PERSON>back, useEffect, useRef, useState } from 'react'
import <PERSON><PERSON>oa<PERSON> from 'react-spinners/RingLoader'
import { toast } from 'sonner'
import { <PERSON><PERSON>oint<PERSON>, Trash, X } from 'lucide-react'
import NoDataIllustratedMessage from '@/app/(app)/_component/IllustratedMessage/NoData'
import ClientPagination from '@/app/(app)/_component/Pagination/ClientPagination'
import Text from '@/app/(app)/_component/Text'
import { Button } from '@/app/(app)/_component/Button'
import { AlertDialog, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/app/(app)/_component/AlertDialog'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '@/app/(app)/_cssComp/FlexContainer'
import GridContainer from '@/app/(app)/_cssComp/GridContainer'
import { ImageMetadata } from '@/app/(app)/_types/image'
import { useImagesPagination } from '@/app/(app)/_hooks/useImagesPagination'
import { apiFetch } from '@/app/(app)/_localApi/util'
import { cn } from '@/lib/utils'
import ImageMetaInfo from '../ImageMetaInfo'

interface Props {
  imageActionChange: (action: string, image?: ImageMetadata | null) => void
  refetchTrigger?: number
  onClick?: () => void
}

function ImageGrid(props: Props) {
  const { imageActionChange, refetchTrigger = 0 } = props

  // Local state for image management
  const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set())
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [loadingTime, setLoadingTime] = useState(0)
  const loadingIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Fetch images using the shared hook
  const {
    images,
    pagination,
    isLoading,
    refetch,
  } = useImagesPagination({
    page: currentPage,
    pageSize: pageSize,
    enabled: true,
  })

  // Handle refetch trigger from parent
  useEffect(() => {
    if (refetchTrigger > 0) {
      refetch()
    }
  }, [refetchTrigger, refetch])

  // Loading timer effect
  useEffect(() => {
    if (isLoading) {
      setLoadingTime(0)
      loadingIntervalRef.current = setInterval(() => {
        setLoadingTime((prev) => prev + 1)
      }, 1000)
    } else {
      if (loadingIntervalRef.current) {
        clearInterval(loadingIntervalRef.current)
        loadingIntervalRef.current = null
      }
      setLoadingTime(0)
    }
    return () => {
      if (loadingIntervalRef.current) {
        clearInterval(loadingIntervalRef.current)
      }
    }
  }, [isLoading])

  // Image selection handlers
  const onImageSelectionChange = useCallback((id: string) => {
    setSelectedImages((prevSet) => {
      const newSet = new Set(prevSet)
      if (newSet.has(id)) {
        newSet.delete(id)
      } else {
        newSet.add(id)
      }
      return newSet
    })
  }, [])

  const clearSelection = useCallback(() => {
    setSelectedImages(new Set())
  }, [])

  const selectAll = useCallback(() => {
    const currentSet = new Set(images.map((image) => image.id))
    setSelectedImages(currentSet)
  }, [images])

  // Pagination handler
  const handlePaginationChange = useCallback((page: number, newPageSize: number) => {
    if (newPageSize !== pageSize) {
      setPageSize(newPageSize)
      setCurrentPage(1) // Reset to page 1 when page size changes
    } else {
      setCurrentPage(page)
    }
    // Clear selections when changing pages
    setSelectedImages(new Set())
  }, [pageSize])

  // Delete selected images
  const deleteSelectedImages = useCallback(async () => {
    try {
      await Promise.all(
        Array.from(selectedImages).map(async (imageId) => {
          await apiFetch(`/api/images/${imageId}`, {
            method: 'DELETE',
          })
        }),
      )

      setSelectedImages(new Set())
      setShowDeleteDialog(false)
      refetch() // Refetch current page after deletion

      toast.success('Images deleted successfully')
    } catch (error) {
      toast.error('Failed to delete some images', {
        description: error instanceof Error ? error.message : String(error),
      })
    }
  }, [selectedImages, refetch])

  return (
    <>
      {/* Action buttons */}
      <FlexContainer
        className="w-full gap-2 sticky top-0 z-11 p-4 bg-bg"
        direction={FlexDirection.ROW}
        align={AlignItems.CENTER}
        justify={JustifyContent.END}
      >
        <Button onClick={selectAll} disabled={images.length === 0}>
          <MousePointer /> Select all
        </Button>
        <Button onClick={clearSelection} disabled={selectedImages.size === 0}>
          <X /> Clear Selection
        </Button>
        <AlertDialog open={showDeleteDialog}>
          <AlertDialogTrigger asChild>
            <Button
              variant="negative"
              disabled={selectedImages.size === 0}
              onClick={() => setShowDeleteDialog(true)}
            >
              <Trash /> Delete All (Selected {selectedImages.size})
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete image</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you wish to delete {selectedImages.size}{' '}
                {selectedImages.size > 1 ? 'images' : 'image'}?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <Button variant="secondary" onClick={() => setShowDeleteDialog(false)}>
                Cancel
              </Button>
              <Button variant="emphasis" onClick={deleteSelectedImages}>
                Continue
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </FlexContainer>

      {/* Content */}
      {!isLoading && images.length === 0 ? (
        <FlexContainer
          wrap
          className="w-full h-full col-span-full row-span-full justify-center items-center justify-self-end self-center"
        >
          <NoDataIllustratedMessage
            title="No images yet!"
            description="Get started and try generating some images!"
          />
        </FlexContainer>
      ) : (
        <FlexContainer className="w-full p-4 mb-4 gap-2" direction={FlexDirection.COL}>
          <ClientPagination
            totalCount={pagination.totalCount}
            currentPage={currentPage}
            currentPageSize={pageSize}
            setCurrentPageSize={(newPageSize) => {
              setPageSize(newPageSize)
              handlePaginationChange(1, newPageSize) // Reset to page 1 when page size changes
            }}
            setCurrentPage={handlePaginationChange}
          />
          <GridContainer
            className={cn(
              'w-full min-h-[inherit] gap-4 grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-3',
            )}
          >
            {isLoading && (
              <FlexContainer
                wrap
                direction={FlexDirection.COL}
                className="w-full rounded-base border-2 shadow-light border-border bg-gray-100 p-4 h-full"
              >
                <FlexContainer
                  direction={FlexDirection.ROW}
                  align={AlignItems.END}
                  justify={JustifyContent.END}
                  className="w-full"
                >
                  <Text variant="description" size="sm">
                    Loading: {loadingTime}s
                  </Text>
                </FlexContainer>
                <FlexContainer
                  direction={FlexDirection.COL}
                  align={AlignItems.CENTER}
                  justify={JustifyContent.CENTER}
                  className="w-full mt-8 gap-2 flex-1"
                >
                  <RingLoader className="mb-2" />
                  <Text variant="description" size="sm" className="text-center">
                    It may take a few seconds to a minute to generate.
                  </Text>
                </FlexContainer>
              </FlexContainer>
            )}
            {images.map((image: ImageMetadata) => (
              <ImageMetaInfo
                key={image.id}
                image={image}
                isSelected={selectedImages.has(image.id)}
                imageActionChange={imageActionChange}
                imageSelectionChange={onImageSelectionChange}
              />
            ))}
          </GridContainer>
        </FlexContainer>
      )}
    </>
  )
}

export default ImageGrid
