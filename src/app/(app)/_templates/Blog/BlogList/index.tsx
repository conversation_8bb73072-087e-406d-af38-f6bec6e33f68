import NextImage from 'next/image'

import { Badge } from '@/app/(app)/_component/Badge'
import Text from '@/app/(app)/_component/Text'
import Title, { HeaderLevel } from '@/app/(app)/_component/Title'
import FlexContainer, { AlignItems, FlexDirection } from '@/app/(app)/_cssComp/FlexContainer'
import GridContainer, { ColSpan, GridItem, RowSpan } from '@/app/(app)/_cssComp/GridContainer'
import { isCategories } from '@/collections/Categories'
import { isMedia } from '@/collections/Media'
import { cn } from '@/lib/utils'
import { Post } from '@/payload-types'
import { getDescription } from '@/utilities/local/blog'
import { Spacing } from '@/utilities/local/css'

interface Props {
  className?: string
  post: Post
  descriptionLimit?: number
}

function BlogList(props: Props) {
  const { className, post, descriptionLimit } = props
  return (
    <GridContainer
      className={cn(
        `w-full h-full grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-4 border-black border-2 rounded-md shadow-[8px_8px_0px_rgba(0,0,0,1)] bg-white ${Spacing.ContentPadding}`,
        className,
      )}
    >
      <GridItem className="relative min-h-[200px] sm:min-h-[200px] md:min-h-48">
        {isMedia(post.image) && (
          <NextImage
            className="rounded-lg object-cover"
            src={post.image.url!}
            fill
            alt={post.image.alt}
          />
        )}
      </GridItem>
      <GridItem>
        <FlexContainer className="w-full gap-5" direction={FlexDirection.COL}>
          <FlexContainer className="w-full gap-2" direction={FlexDirection.COL}>
            <Title level={HeaderLevel.H4} className="text-lg">
              {post.title}
            </Title>
            <FlexContainer
              className="w-full gap-2"
              direction={FlexDirection.ROW}
              align={AlignItems.CENTER}
            >
              {isCategories(post.category) && <Badge>{post.category[0].name}</Badge>}
              {post.readingTime && (
                <Text variant="description" size="sm">
                  ✨ {post.readingTime} min read
                </Text>
              )}
            </FlexContainer>
          </FlexContainer>
          {post.richText && (
            <Text variant="body">
              {getDescription(post.richText.root.children, descriptionLimit ?? 20)}...
            </Text>
          )}
        </FlexContainer>
      </GridItem>
    </GridContainer>
  )
}

export default BlogList
