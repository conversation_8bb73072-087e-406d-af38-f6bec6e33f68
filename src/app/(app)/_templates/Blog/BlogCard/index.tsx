import NextImage from 'next/image'

import { Badge } from '@/app/(app)/_component/Badge'
import Text from '@/app/(app)/_component/Text'
import Title, { HeaderLevel } from '@/app/(app)/_component/Title'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '@/app/(app)/_cssComp/FlexContainer'
import GridContainer, { GridItem } from '@/app/(app)/_cssComp/GridContainer'
import { isCategories } from '@/collections/Categories'
import { isMedia } from '@/collections/Media'
import { isUser } from '@/collections/Users'
import { cn } from '@/lib/utils'
import { Post } from '@/payload-types'
import { getDescription } from '@/utilities/local/blog'
import { Spacing } from '@/utilities/local/css'
import { formatTimestamp } from '@/utilities/local/date'

interface Props {
  className?: string
  post: Post
  descriptionLimit?: number
}

function BlogCard(props: Props) {
  const { className, post, descriptionLimit } = props
  return (
    <GridContainer
      className={cn(
        `w-full h-full grid-cols-1 gap-3 border-black border-2 rounded-md shadow-[8px_8px_0px_rgba(0,0,0,1)] bg-white ${Spacing.ContentPadding}`,
        className,
      )}
    >
      <GridItem
        //direction={FlexDirection.COL}
        className={`w-full gap-4`}
      >
        {isMedia(post.image) && (
          <NextImage
            className="rounded-lg"
            src={post.image.url!}
            width={post.image.width!}
            height={post.image.height!}
            alt={post.image.alt}
          />
        )}
      </GridItem>
      <GridItem>
        <Title level={HeaderLevel.H3}>{post.title}</Title>
      </GridItem>
      <GridItem>
        {post.richText && (
          <Text>{getDescription(post.richText.root.children, descriptionLimit ?? 10)}...</Text>
        )}
      </GridItem>
      <GridItem>
        <FlexContainer
          direction={FlexDirection.ROW}
          justify={JustifyContent.BETWEEN}
          align={AlignItems.CENTER}
          className="w-full mt-auto"
        >
          {isUser(post.author) && (
            <div>
              By {post.author.author_name} . {formatTimestamp(post.publishDate)}
            </div>
          )}
          {isCategories(post.category) &&
            post.category.map((category, i) => {
              return <Badge key={i}>{category.name}</Badge>
            })}
        </FlexContainer>
      </GridItem>
    </GridContainer>
  )
}

export default BlogCard
