'use client'

import { Library, Palette, Settings2, <PERSON>rk<PERSON> } from 'lucide-react'
import Link from 'next/link'
import ColorPalette from '../../../../public/media/static/illustration/colorPalette.svg'
import GenerateArt from '../../../../public/media/static/illustration/generateArt.svg'
import ImageLibrary from '../../../../public/media/static/illustration/imageLibrary.svg'
import Settings from '../../../../public/media/static/illustration/settings.svg'
import ImageCard from '../_component/ImageCard'
import Title, { HeaderLevel } from '../_component/Title'
import Container from '../_cssComp/Container'
import FlexContainer, { AlignItems, FlexDirection, JustifyContent } from '../_cssComp/FlexContainer'

import { useAuthState } from '../_state/authState'

function DashboardPage() {
  const HOST_URL = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'
  const user = useAuthState((state) => state.user)

  return (
    <Container>
      <FlexContainer className={'w-full h-full gap-4'}>
        <Title level={HeaderLevel.H1} className={'pt-3'}>
          Welcome! {user?.firstName} {user?.lastName}
        </Title>
        <FlexContainer
          className={'w-full h-full gap-4'}
          direction={FlexDirection.ROW}
          wrap
          align={AlignItems.CENTER}
          justify={JustifyContent.CENTER}
        >
          <Link href={`${HOST_URL}/dashboard/image-generation`}>
            <ImageCard
              title={
                <FlexContainer align={AlignItems.CENTER} className="gap-2">
                  <Sparkles /> Generate Art
                </FlexContainer>
              }
              image={GenerateArt}
              caption={'Generate art'}
              className="active:translate-x-box-shadow-x active:translate-y-box-shadow-y active:shadow-none dark:active:shadow-none bg-accent3"
            >
              Get started generating art of your dreams!
            </ImageCard>
          </Link>
          <Link href={`${HOST_URL}/dashboard/settings`}>
            <ImageCard
              title={
                <FlexContainer align={AlignItems.CENTER} className="gap-2">
                  <Settings2 /> Settings
                </FlexContainer>
              }
              image={Settings}
              caption={'Settings'}
              className="active:translate-x-box-shadow-x active:translate-y-box-shadow-y active:shadow-none dark:active:shadow-none bg-accent3"
            >
              Customize your experience
            </ImageCard>
          </Link>
          <Link href={`${HOST_URL}/dashboard/settings`}>
            <ImageCard
              title={
                <FlexContainer align={AlignItems.CENTER} className="gap-2">
                  <Palette /> Coloring App
                </FlexContainer>
              }
              image={ColorPalette}
              caption={'Settings'}
              className="active:translate-x-box-shadow-x active:translate-y-box-shadow-y active:shadow-none dark:active:shadow-none bg-accent3"
            >
              Start coloring your creations!
            </ImageCard>
          </Link>
          <Link href={`${HOST_URL}/dashboard/image-library`}>
            <ImageCard
              title={
                <FlexContainer align={AlignItems.CENTER} className="gap-2">
                  <Library /> Image Library
                </FlexContainer>
              }
              image={ImageLibrary}
              caption={'Image Library'}
              className="active:translate-x-box-shadow-x active:translate-y-box-shadow-y active:shadow-none dark:active:shadow-none bg-accent3"
            >
              Need inspiration? Check out our Image Library!
            </ImageCard>
          </Link>
        </FlexContainer>
      </FlexContainer>
    </Container>
  )
}

export default DashboardPage
