'use client'
import { zodResolver } from '@hookform/resolvers/zod'
import { QueryClient, useMutation, useQuery } from '@tanstack/react-query'
import { Coins, IdCard, Save } from 'lucide-react'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { MoonLoader } from 'react-spinners'
import { toast } from 'sonner'
import { z } from 'zod'
import { IconSize, Margin } from '@/utilities/local/css'
import { deepEqual } from '@/utilities/local/object'
import { capitalizeAndReplace } from '@/utilities/local/string'
import { Button } from '../../_component/Button'
import Card from '../../_component/Card/CardApplication'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../../_component/Form'
import { Input } from '../../_component/Input'
import { Label } from '../../_component/Label'
import Title, { HeaderLevel } from '../../_component/Title'
import Container from '../../_cssComp/Container'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../_cssComp/FlexContainer'
import GridContainer, { ColSpan, GridItem, RowSpan } from '../../_cssComp/GridContainer'
import { fetchSelfUser, updateUser } from '../../_localApi/users'
import { useAuthState } from '../../_state/authState'
import HighlightNumericCard from '../../_templates/NumericCard/HighlightNumericCard'

function SettingsProfile() {
  const queryClient = new QueryClient()
  const user = useAuthState((state) => state.user)
  const setUser = useAuthState((state) => state.setUser)
  const [bLoadingSave, setBLoadingSave] = useState(false)

  const updateUserMut = useMutation({
    mutationFn: (data: { firstName: string; lastName: string }) => {
      return updateUser(data.firstName, data.lastName)
    },
    onMutate: () => {
      setBLoadingSave(true)
    },
    onSuccess: async (result) => {
      queryClient.invalidateQueries({ queryKey: ['user'] })
      setUser({
        id: result.data.id,
        firstName: result.data.firstName,
        lastName: result.data.lastName,
        email: result.data.email,
        role: result.data.role,
        freeCredits: result.data.freeCredits,
        paidCredits: result.data.paidCredits,
        rolloverCredits: result.data.rolloverCredits,
      })
      toast.success('Your profile settings have been saved.', {
        position: 'top-right',
      })
    },
    onError: () => {
      toast.error('Something went wrong. Please try again.', {
        position: 'top-right',
      })
    },
    onSettled: () => {
      setBLoadingSave(false)
    },
  })

  const userQuery = useQuery({
    queryKey: ['user', updateUserMut.data],
    queryFn: () => fetchSelfUser(),
  })

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    if (form.formState.isDirty) {
      updateUserMut.mutateAsync({
        firstName: values.firstName,
        lastName: values.lastName,
      })
    } else {
      toast.info('No changes were made.', {
        position: 'top-right',
      })
    }
  }

  useEffect(() => {
    if (userQuery.data) {
      setUser({
        firstName: userQuery.data.data.firstName,
        lastName: userQuery.data.data.lastName,
        email: userQuery.data.data.email,
        role: userQuery.data.data.role,
        id: userQuery.data.data.id,
        freeCredits: userQuery.data.data.freeCredits,
        paidCredits: userQuery.data.data.paidCredits,
        rolloverCredits: userQuery.data.data.rolloverCredits,
      })
    }
  }, [userQuery.dataUpdatedAt])

  const formSchema = z.object({
    firstName: z.string().min(1, {
      message: 'Name is required',
    }),
    lastName: z.string().min(1, {
      message: 'Name is required',
    }),
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: useMemo(() => {
      return {
        firstName: user?.firstName || '',
        lastName: user?.lastName || '',
      }
    }, [user]),
  })

  useEffect(() => {
    if (user) {
      form.reset(user)
    }
  }, [user, userQuery.dataUpdatedAt])

  const TitleBar = () => {
    return (
      <FlexContainer justify={JustifyContent.BETWEEN} align={AlignItems.CENTER} className="w-full">
        <Title level={HeaderLevel.H3}>My Profile</Title>
        <Button
          variant="emphasis"
          round="round"
          onClick={form.handleSubmit(onSubmit)}
          disabled={!form.formState.isDirty}
        >
          {bLoadingSave ? (
            <MoonLoader size={IconSize.Small} color={'#FFFFFF'} />
          ) : (
            <FlexContainer className="gap-2" align={AlignItems.CENTER}>
              <Save size={IconSize.Small} /> Save
            </FlexContainer>
          )}
        </Button>
      </FlexContainer>
    )
  }
  return (
    <Container>
      <FlexContainer direction={FlexDirection.COL} className="w-full gap-8">
        <GridContainer
          className={`gap-4 mt-[${Margin.Medium}] w-full pr-2`}
          rows={12}
          autoResponsive={false}
        >
          <GridItem colSpan={ColSpan.SPAN_12}>
            <Title className="mt-4">Settings</Title>
          </GridItem>
          <GridItem colSpan="col-span-12 md:col-span-6">
            <HighlightNumericCard
              title="Available Credits"
              value={String(
                (userQuery.data?.data.freeCredits || 0) + (userQuery.data?.data.paidCredits || 0),
              )}
              icon={<Coins size={IconSize.Medium} />}
            >
              Renews on: 01/01/2015
            </HighlightNumericCard>
          </GridItem>
          <GridItem colSpan="col-span-12 md:col-span-6">
            <HighlightNumericCard
              title="Current Membership"
              value={capitalizeAndReplace(user?.role)}
              icon={<IdCard size={IconSize.Medium} />}
            >
              Billing Date: 01/01/2015
            </HighlightNumericCard>
          </GridItem>
        </GridContainer>
        <Card title={<TitleBar />}>
          <FlexContainer direction={FlexDirection.COL} className="space-y-4 w-full">
            <FlexContainer direction={FlexDirection.COL}>
              <Label>Email:</Label>
              <b>{user?.email}</b>
            </FlexContainer>
            <Form {...form}>
              <FlexContainer direction={FlexDirection.COL} className="space-y-8 font-bold w-full">
                <GridContainer className="gap-4 w-full">
                  <GridItem colSpan="col-span-12 md:col-span-6">
                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem className={'w-full'}>
                          <FormLabel>First Name:</FormLabel>
                          <FormControl>
                            <Input className={'w-full'} placeholder="" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </GridItem>
                  <GridItem colSpan="col-span-12 md:col-span-6">
                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem className={'w-full'}>
                          <FormLabel>Last name:</FormLabel>
                          <FormControl>
                            <Input className={'w-full'} placeholder="" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </GridItem>
                </GridContainer>
                {/* <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email:</FormLabel>
                    <FormControl>
                      <Input className={'w-full'} placeholder="" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              /> */}
              </FlexContainer>
            </Form>
          </FlexContainer>
        </Card>
      </FlexContainer>
    </Container>
  )
}

export default SettingsProfile
