import { get, set, del } from 'idb-keyval'
import { useEffect, useState } from 'react'
import { create, StateCreator } from 'zustand'
import { createJSONStorage, persist, StateStorage } from 'zustand/middleware'
import { imageUrlToBase64 } from '@/utilities/local/image'
import { fetchCompletedImages } from '../_localApi/imageGeneration'

export interface ImageMetadata {
  id: string
  src: string
  width: number
  height: number
  prompt?: string
  actionType: string
  unsplashSrc?: string
}

interface ImageState {
  images: ImageMetadata[]
  setImages: (image: ImageMetadata[]) => void
  loadInitialImages: () => Promise<void>
  isLoading: boolean
  setIsLoading: (v: boolean) => void
  clearImages: () => void
  _hasHydrated: boolean
  _setHasHydrated: (state: boolean) => void
}

// Custom storage object
export const indexDBStorage: StateStorage = {
  getItem: async (name: string): Promise<string | null> => {
    console.log(name, 'has been retrieved')
    const value = await get(name)
    if (!value) return null
    // Convert stored object back to JSON string for zustand
    return JSON.stringify(value)
  },
  setItem: async (name: string, value: string): Promise<void> => {
    // value is a JSON string here
    // Parse it back to object before storing in idb
    const obj = JSON.parse(value)
    await set(name, obj)
  },
  removeItem: async (name: string): Promise<void> => {
    console.log(name, 'has been deleted')
    await del(name)
  },
}

// Add onRehydrateStorage to handle rehydration
export const createIndexDBStore = <T extends object>({
  name,
  handler,
}: {
  name: string
  handler: StateCreator<T>
}) =>
  create(
    persist(handler, {
      name, // name of the item in the storage (must be unique)
      storage: createJSONStorage(() => indexDBStorage), // (optional) by default, 'localStorage' is used
      onRehydrateStorage: () => (state) => {
        console.log('Rehydration callback triggered', state)
        setTimeout(() => {
          useImageState.getState()._setHasHydrated(true)
        }, 0)
      },
    }),
  )

// Usage
export const useImageState = createIndexDBStore<ImageState>({
  name: 'images',
  handler: (set, get) => ({
    images: [],
    setImages: (event: ImageMetadata[]) => set({ images: event }),
    isLoading: false,
    setIsLoading: (v: boolean) => set({ isLoading: v }),
    loadInitialImages: async () => {
      if (get().images.length > 0) return
      try {
        set({ isLoading: true })
        const response = await fetchCompletedImages()
        const generatedImages = response.data.images
        const imageMetadata = await Promise.all(
          generatedImages.map(async (image) => {
            if (!image.url) return null
            return {
              id: image.id,
              src: image.url,
              width: image.width as number,
              height: image.height as number,
              prompt: image.prompt as string,
              actionType: image.taskType as string,
            }
          }),
        )
        const nonNullImageMetadata = imageMetadata.filter(
          (image): image is NonNullable<typeof image> => image != null,
        )
        set({ images: nonNullImageMetadata })
      } catch (error) {
        console.error('Failed to load initial images', error)
      } finally {
        set({ isLoading: false })
      }
    },
    clearImages: () => set({ images: [] }),
    _hasHydrated: false,
    _setHasHydrated: (state: boolean) => set({ _hasHydrated: state }),
  }),
})

// Create a hook to safely use the store with SSR
export function useHydratedImageState<T>(selector: (state: ImageState) => T, defaultValue: T): T {
  const [hydrated, setHydrated] = useState(false)
  const value = useImageState(selector)
  const hasHydrated = useImageState((state) => state._hasHydrated)

  useEffect(() => {
    setHydrated(hasHydrated)
  }, [hasHydrated])

  // Return default value if not yet hydrated on client
  if (!hydrated) {
    return defaultValue
  }

  return value
}
