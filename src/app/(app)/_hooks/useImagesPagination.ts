import { useQuery } from '@tanstack/react-query'
import { useMemo } from 'react'
import { GeneratedImage } from '@/payload-types'
import { fetchUserImages } from '../_localApi/imageGeneration'
import { ImageMetadata } from '../_types/image'

interface UseImagesPaginationOptions {
  page?: number
  pageSize?: number
  excludeIds?: string[]
  enabled?: boolean
  refetchInterval?: number | false
}

interface UseImagesPaginationResult {
  images: ImageMetadata[]
  pagination: {
    totalCount: number
    currentPage: number
    totalPages: number
    pageSize: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
  isLoading: boolean
  error: Error | null
  refetch: () => void
}

export function useImagesPagination(options: UseImagesPaginationOptions = {}): UseImagesPaginationResult {
  const {
    page = 1,
    pageSize = 10,
    excludeIds = [],
    enabled = true,
    refetchInterval = false
  } = options

  const {
    data,
    error,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['images-pagination', page, pageSize, excludeIds],
    queryFn: async () => {
      console.log(`Fetching images for page ${page}, pageSize ${pageSize}`)
      const response = await fetchUserImages(excludeIds, page, pageSize)
      const generatedImages: GeneratedImage[] = response.data.images
      const pagination = response.data.pagination
      
      // Transform GeneratedImage[] to ImageMetadata[]
      const imagePromises = generatedImages.map(async (image: GeneratedImage) => {
        if (!image.url) return null
        return {
          id: image.id,
          src: image.url,
          width: image.width as number,
          height: image.height as number,
          prompt: image.prompt as string,
          actionType: image.taskType as string,
        }
      })

      const imagesMetadata = await Promise.all(imagePromises)
      const filteredImages = imagesMetadata.filter((image) => image !== null) as ImageMetadata[]
      
      return {
        images: filteredImages,
        pagination: pagination
      }
    },
    enabled,
    refetchInterval,
  })

  const defaultPagination = useMemo(() => ({
    totalCount: 0,
    currentPage: page,
    totalPages: 0,
    pageSize,
    hasNextPage: false,
    hasPrevPage: false,
  }), [page, pageSize])

  return {
    images: data?.images || [],
    pagination: data?.pagination || defaultPagination,
    isLoading,
    error: error as Error | null,
    refetch,
  }
}
