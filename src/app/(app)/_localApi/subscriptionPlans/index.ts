// import { GetSubscriptionPlansResults } from '../../api/subscription-plans/route'
// import { ApiResponse, apiFetch } from '../util'

// export async function getAllSubscriptionPlans(): Promise<ApiResponse<GetSubscriptionPlansResults>> {
//   const headers = {
//     method: 'GET',
//     headers: {
//       'Content-Type': 'application/json',
//     },
//   } as RequestInit

//   const response = await apiFetch<GetSubscriptionPlansResults>(`/api/subscription-plans`, headers)

//   return response
// }

// export async function getSubscriptionPlanByName(name: string, subscriptionDuration: string) {
//   const headers = {
//     method: 'POST',
//     headers: {
//       'Content-Type': 'application/json',
//     },
//   } as RequestInit

//   const response = await apiFetch<GetSubscriptionPlansResults>(
//     `/api/subscription-plans/${name}?subscriptionDuration=${subscriptionDuration}`,
//     headers,
//   )

//   return response
// }
