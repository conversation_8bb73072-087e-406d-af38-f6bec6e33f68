import { PublicUser } from '@/payload-types'

export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  customerId: string
  freeCredits: number
  paidCredits: number
  rolloverCredits: number
  address: string
  country: string
  state: string
  city: string
  role: PublicUser['role']
}

export interface UserWithAuth {
  exp?: number
  token?: string
  user: User
}

export class UserDTO {
  publicUser: PublicUser
  constructor(publicUser: PublicUser) {
    this.publicUser = publicUser
  }
  distil() {
    return {
      id: this.publicUser.id,
      email: this.publicUser.email,
      firstName: this.publicUser.firstName,
      lastName: this.publicUser.lastName,
      freeCredits: this.publicUser.freeCredits,
      paidCredits: this.publicUser.paidCredits,
      rolloverCredits: this.publicUser.rolloverCredits,
      address: this.publicUser.address,
      country: this.publicUser.country,
      state: this.publicUser.state,
      city: this.publicUser.city,
      role: this.publicUser.role,
    }
  }
}
