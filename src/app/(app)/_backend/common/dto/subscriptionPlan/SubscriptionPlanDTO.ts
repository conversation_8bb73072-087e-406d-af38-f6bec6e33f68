import { SubscriptionPlan } from '@/payload-types'

export class SubscriptionPlanDTO {
  subscriptionPlan: SubscriptionPlan
  constructor(subcriptionPlan: SubscriptionPlan) {
    this.subscriptionPlan = subcriptionPlan
  }
  distil() {
    return {
      id: this.subscriptionPlan.id,
      name: this.subscriptionPlan.name,
      description: this.subscriptionPlan.description,
      stripePriceId: this.subscriptionPlan.stripePriceId,
      price: this.subscriptionPlan.price,
      subscription_duration: this.subscriptionPlan.subscription_duration,
    }
  }
}
