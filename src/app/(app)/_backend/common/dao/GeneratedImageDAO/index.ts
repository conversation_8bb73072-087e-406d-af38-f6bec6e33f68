import { BasePayload } from 'payload'
import { GeneratedImage } from '@/payload-types'
import { GeneratedImageStatus } from '@/types/GeneratedImageStatus'
import { TaskType } from '@/types/TaskType'

class GeneratedImageDAO {
  private payload: BasePayload

  constructor(payload: BasePayload) {
    this.payload = payload
  }

  async createGeneratingEntries(
    imageIds: string[],
    taskType: TaskType,
    userId: string,
    prompt?: string,
  ) {
    for (const imageId of imageIds) {
      const data: any = {
        id: imageId,
        taskType: taskType,
        status: GeneratedImageStatus.GENERATING,
        createdBy: userId,
      }
      if (prompt) {
        data.prompt = prompt
      }
      await this.payload.create({
        collection: 'generated-images',
        data,
      })
    }
  }

  async updateEntriesStatus(imageIds: string[], status: GeneratedImageStatus) {
    for (const imageId of imageIds) {
      try {
        await this.payload.update({
          collection: 'generated-images',
          id: imageId,
          data: { status },
        })
      } catch (error) {
        console.error(`Failed to update status for image ${imageId}:`, error)
      }
    }
  }

  async updateImageFile(imageId: string, buffer: Buffer) {
    const filename = `${imageId}.png`
    const result = await this.payload.update({
      collection: 'generated-images',
      id: imageId,
      file: {
        name: filename,
        mimetype: 'image/png',
        data: buffer,
        size: buffer.byteLength,
      },
      data: {
        status: GeneratedImageStatus.COMPLETED,
      },
    })

    return result
  }

  async updateCompletedImage(imageId: string, width: number, height: number) {
    this.payload.update({
      collection: 'generated-images',
      id: imageId,
      data: {
        status: GeneratedImageStatus.COMPLETED,
        width: width,
        height: height,
      },
    })
  }

  async getCompletedImageIds(userId: string): Promise<string[]> {
    const images = await this.payload.find({
      collection: 'generated-images',
      where: {
        status: { equals: GeneratedImageStatus.COMPLETED },
        createdBy: { equals: userId },
      },
    })
    return images.docs.map((image: any) => image.id)
  }

  async getImagesByStatus(
    userId: string,
    status: string[],
    excludeIds: string[] = [],
    page: number = 1,
    limit: number = 20,
  ): Promise<{ docs: GeneratedImage[]; totalDocs: number; totalPages: number; page: number; limit: number; hasNextPage: boolean; hasPrevPage: boolean }> {
    const conditions: any[] = [{ createdBy: { equals: userId } }, { status: { in: status } }]

    if (excludeIds.length > 0) {
      conditions.push({ id: { not_in: excludeIds } })
    }

    const images = await this.payload.find({
      collection: 'generated-images',
      pagination: true,
      page: page,
      limit: limit,
      sort: '-createdAt', // Sort by newest first
      where: {
        and: conditions,
      },
    })

    return {
      docs: images.docs,
      totalDocs: images.totalDocs,
      totalPages: images.totalPages,
      page: images.page || page,
      limit: images.limit || limit,
      hasNextPage: images.hasNextPage || false,
      hasPrevPage: images.hasPrevPage || false,
    }
  }

  async hasGeneratingTask(userId: string): Promise<boolean> {
    const result = await this.payload.find({
      collection: 'generated-images',
      where: {
        status: { equals: GeneratedImageStatus.GENERATING },
        createdBy: { equals: userId },
      },
      limit: 1,
    })
    return result.docs.length > 0
  }
}

export default GeneratedImageDAO
