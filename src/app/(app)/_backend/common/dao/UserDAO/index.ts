import { StatusCodes } from 'http-status-codes'
import { BasePayload } from 'payload'
import { PublicUser } from '@/payload-types'
import { HTTPException } from '../../exception'

class UserDAO {
  private payload: BasePayload

  constructor(payload: BasePayload) {
    this.payload = payload
  }

  async getUsersByEmailId(email: string) {
    const result = await this.payload.find({
      collection: 'public-users',
      where: {
        email: {
          equals: email,
        },
      },
    })

    return result
  }

  async getUserVerificationToken(email: string) {
    const result = await this.payload.find({
      collection: 'public-users',
      where: {
        email: {
          equals: email,
        },
      },
      showHiddenFields: true,
    })
    return result.docs[0].verificationToken
  }

  async setUserVerified(email: string) {
    this.payload.update({
      collection: 'public-users',
      where: {
        email: {
          equals: email,
        },
      },
      data: {
        verified: true,
      },
    })
  }

  async createNewUser(
    email: string,
    password: string,
    firstName: string,
    lastName: string,
    freeCredits: number,
    paidCredits: number,
    rolloverCredits: number,
    role: PublicUser['role'],
    verified: boolean,
    verificationToken: string,
  ) {
    const user = await this.payload.create({
      collection: 'public-users',
      data: {
        firstName: firstName,
        lastName: lastName,
        email: email,
        password: password,
        freeCredits: freeCredits,
        paidCredits: paidCredits,
        rolloverCredits: rolloverCredits,
        role: role,
        verified: verified,
        verificationToken: verificationToken,
      },
    })

    return user
  }

  async loginUser(email: string, password: string) {
    const user = await this.payload.login({
      collection: 'public-users',
      data: {
        email,
        password,
      },
    })

    return user
  }

  async genericUpdateUser(id: string, data: Partial<PublicUser>) {
    const userInfo = await this.payload.update({
      collection: 'public-users',
      id: id,
      data: data,
    })

    return userInfo
  }

  async updateUserOnUser(id: string, firstName: string, lastName: string) {
    try {
      const userInfo = await this.payload.update({
        collection: 'public-users',
        id: id,
        data: {
          firstName,
          lastName,
          updatedAt: new Date().toISOString(),
        },
      })
      return userInfo
    } catch (error) {
      console.log(error)
      throw new HTTPException('Failed to update user', StatusCodes.INTERNAL_SERVER_ERROR)
    }
  }
}

export default UserDAO
