import * as OTPAuth from 'otpauth'
import { SendEmailOptions } from 'payload'
import crypto from 'crypto'
import { mailgun } from '@/lib/mailgun'
import { PublicUser } from '@/payload-types'
import UserDAO from '../../dao/UserDAO'
import { UserDTO } from '../../dto/user/UserDTO'
import { UnauthenticatedUser, UnauthorizedUser, UserAlreadyExists } from '../../exception'
import { backendApiFetch } from '../../utils/api/apiHelper'
import { _verifyJWT } from '../../utils/auth'

/**
 * UserService class
 * Register a new user
 *  - email/password flow (need email verification)
 *  - Google OAuth flow (no need email verification)
 * Login a user
 *  - email/password flow
 *  - Google OAuth flow
 * Get user info
 *  - Get user info by email
 *  - Get user info by id
 * Update a user?
 */

interface TokenRefreshResponse {
  message: string
  refreshedToken: string
  exp: number
  user: {
    email: string
    id: string
    collection: string
  }
}

class UserService {
  private userDAO: UserDAO
  private HOST_URL = process.env.NEXT_PUBLIC_HOST

  constructor(userDAO: UserDAO) {
    this.userDAO = userDAO
  }

  async register(
    email: string,
    firstName: string,
    lastName: string,
    oauth?: boolean,
    password?: string,
  ) {
    const users = await this.userDAO.getUsersByEmailId(email)
    if (users.docs.length > 0) {
      if (users.docs[0].verified) {
        throw new UserAlreadyExists()
      } else {
        // user created but not verified, resend verification email
        this.sendVerificationEmail(email)
        return new UserDTO(users.docs[0]).distil()
      }
    }

    let newUser: PublicUser
    if (oauth) {
      newUser = await this._oauthRegister(email, firstName, lastName)
    } else {
      newUser = await this._emailRegister(email, firstName, lastName, password as string)
    }

    const userDTO = new UserDTO(newUser).distil()
    return userDTO
  }

  async _oauthRegister(email: string, firstName: string, lastName: string) {
    const randomPassword = crypto.randomBytes(20).toString('hex')
    const newUser = await this.userDAO.createNewUser(
      email,
      randomPassword,
      firstName,
      lastName,
      0,
      0,
      0,
      'free',
      true,
      '',
    )
    return newUser
  }

  async _generateVerificationToken() {
    const secret = new OTPAuth.Secret({ size: 20 })
    return secret.base32
  }

  async _generateOtp(secret: string) {
    const totp = new OTPAuth.TOTP({
      algorithm: 'SHA512',
      digits: 6,
      period: 30,
      secret: secret,
    })
    const token = totp.generate()
    return token
  }

  async _sendVerificationEmail(recipient: string, token: string) {
    const emailOptions: SendEmailOptions = {
      to: recipient,
      from: '<EMAIL>',
      subject: 'Welcome to LineArt!',
      text: `You have successfully registered with LineArt! Your OTP code is: ${token}. Start your journey with LineArt today!`,
    }
    mailgun.sendEmail(emailOptions)
  }

  async sendVerificationEmail(email: string) {
    const verificationToken = await this.userDAO.getUserVerificationToken(email)
    const token = await this._generateOtp(verificationToken as string)
    this._sendVerificationEmail(email, token)
  }

  async _emailRegister(email: string, firstName: string, lastName: string, password: string) {
    const verificationToken = await this._generateVerificationToken()
    const newUser = await this.userDAO.createNewUser(
      email,
      password,
      firstName,
      lastName,
      0,
      0,
      0,
      'free',
      false,
      verificationToken,
    )

    const token = await this._generateOtp(verificationToken)
    this._sendVerificationEmail(email, token)
    return newUser
  }

  async verifyOTPCode(email: string, code: string) {
    const secret = await this.userDAO.getUserVerificationToken(email)

    if (!secret) {
      throw new UnauthorizedUser()
    }

    try {
      const totp = new OTPAuth.TOTP({
        algorithm: 'SHA512',
        digits: 6,
        period: 30,
        secret: secret,
      })
      const isValid = totp.validate({ token: code, window: 1 })
      console.log(`OTP ${code} is valid: ${isValid}`)
      return isValid !== null
    } catch (err) {
      // Possible errors: malformed token, verification failed
      console.error('OTP verification error:', err)
      return false
    }
  }

  async loginUser(email: string, password: string) {
    try {
      const userLogin = await this.userDAO.loginUser(email, password)
      return userLogin
    } catch (error) {
      throw new UnauthenticatedUser()
    }
  }

  async verifyUser(token: string | undefined | null) {
    const verified = await _verifyJWT(token)
    return verified
  }

  async getUserByEmailId(email: string) {
    const user = await this.userDAO.getUsersByEmailId(email)
    if (user.docs.length === 0) {
      return null
    } else if (user.docs.length > 1) {
      throw new Error('Multiple users found with the same email')
    }
    const userDTO = new UserDTO(user.docs[0]).distil()
    return userDTO
  }

  //Will be updated in the future with more fields if necessary
  async updateUser(id: string, firstName: string, lastName: string) {
    const getUser = await this.userDAO.updateUserOnUser(id, firstName, lastName)
    const userDTO = new UserDTO(getUser).distil()
    return userDTO
  }

  async updateUserSubscriptionRecord(
    id: string,
    subscriptionRecordId: number,
    plan: 'admin' | 'editor' | 'subscriber' | 'free' | undefined,
    credit: number,
  ) {
    const getUser = await this.userDAO.genericUpdateUser(id, {
      subscription: subscriptionRecordId,
      role: plan,
      freeCredits: credit,
    })
    const userDTO = new UserDTO(getUser).distil()
    return userDTO
  }

  async refreshToken() {
    const headers = {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
    } as RequestInit
    const response = await backendApiFetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/public-users/refresh-token`,
      headers,
      'Something went wrong refreshing token.',
    )
    const result = (await response.json()) as TokenRefreshResponse
    return result
  }
}

export default UserService
