import { StatusCodes } from 'http-status-codes'

export class HTTPException extends Error {
  statusCode: number

  constructor(message: string, statusCode = StatusCodes.INTERNAL_SERVER_ERROR) {
    super(message)
    this.statusCode = statusCode
  }
}

export class UserAlreadyExists extends HTTPException {
  constructor(
    message: string = 'User already exists in our records. Please login instead.',
    statusCode: number = StatusCodes.CONFLICT,
  ) {
    super(message, statusCode)
  }
}

export class NotFound extends HTTPException {
  constructor(message: string = 'Resource not found.', statusCode: number = StatusCodes.NOT_FOUND) {
    super(message, statusCode)
  }
}

export class UnauthorizedUser extends HTTPException {
  constructor(
    message: string = 'User is unauthorized to access resource.',
    statusCode: number = StatusCodes.FORBIDDEN,
  ) {
    super(message, statusCode)
  }
}

export class UnauthenticatedUser extends HTTPException {
  constructor(
    message: string = 'Either your email or password is incorrect, please try again.',
    statusCode: number = StatusCodes.UNAUTHORIZED,
  ) {
    super(message, statusCode)
  }
}

export class <PERSON><PERSON><PERSON>xpired extends HTTPException {
  constructor(
    message: string = 'Your session has expired. Please login again.',
    statusCode: number = StatusCodes.UNAUTHORIZED,
  ) {
    super(message, statusCode)
  }
}

export class InvalidToken extends HTTPException {
  constructor(
    message: string = 'There is an issue with your login. Please try again.',
    statusCode: number = StatusCodes.UNAUTHORIZED,
  ) {
    super(message, statusCode)
  }
}

export class InvalidSlug extends HTTPException {
  constructor(
    message: string = 'Blog post cannot be found, refine your search and try again.',
    statusCode: number = StatusCodes.NOT_FOUND,
  ) {
    super(message, statusCode)
  }
}

export class ExternalServiceError extends HTTPException {
  constructor(
    message: string = 'There is an issue with the external service. Please try again.',
    statusCode: number = StatusCodes.INTERNAL_SERVER_ERROR,
  ) {
    super(message, statusCode)
  }
}
