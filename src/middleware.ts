import { RequestCookie } from 'next/dist/compiled/@edge-runtime/cookies'
import { NextRequest, NextResponse } from 'next/server'
import { _verifyJWT } from './app/(app)/_backend/common/utils/auth'

enum Routing {
  Dashboard = '/dashboard',
  Login = '/login',
  Register = '/register',
  API_User = '/api/user',
}

export interface TokenRefreshResponse {
  message: string
  refreshedToken: string
  exp: number // Unix timestamp
  user: {
    email: string
    id: string
    collection: string
  }
}

async function refreshToken(token: RequestCookie): Promise<TokenRefreshResponse | null> {
  try {
    const FIVE_MINUTES_IN_SECONDS = 5 * 60
    const base64Payload = token.value.split('.')[1]
    const payload = JSON.parse(atob(base64Payload))
    const exp = payload.exp
    const now = Math.floor(Date.now() / 1000)
    if (exp - now <= FIVE_MINUTES_IN_SECONDS) {
      let cookie = `${encodeURIComponent('la-token')}=${encodeURIComponent(token.value)}; path=/; SameSite=Lax; Secure=${process.env.NODE_ENV === 'production'}`
      const req = await fetch(`${process.env.NEXT_PUBLIC_HOST}/la_api/public-users/refresh-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Cookie: cookie,
        },
      })
      const response = await req.json()
      return response
    } else {
      return null
    }
  } catch (error) {
    console.error('Error decoding token or refreshing:', error)
    return null
  }
}

async function authMiddleware(request: NextRequest) {
  let authToken = request.cookies.get('la-token')

  if (
    // unauthenticated routes
    request.nextUrl.pathname.startsWith('/api/user/register') ||
    request.nextUrl.pathname.startsWith('/api/user/resendCode') ||
    request.nextUrl.pathname.startsWith('/api/user/verifyCode') ||
    request.nextUrl.pathname.startsWith('/api/user/login') ||
    request.nextUrl.pathname.startsWith('/api/oauth')
  ) {
    return NextResponse.next()
  } else if (
    request.nextUrl.pathname.startsWith('/api') ||
    request.nextUrl.pathname.startsWith('/dashboard')
  ) {
    if (!authToken) {
      return NextResponse.redirect(new URL('/login', request.url))
    }
    const verifyUser = await _verifyJWT(authToken.value)
    if (!verifyUser) {
      return NextResponse.redirect(new URL('/login', request.url))
    }

    const requestHeaders = new Headers(request.headers)
    requestHeaders.set('x-user', JSON.stringify(verifyUser))
    const response = NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    })

    if (!request.url.startsWith('/api/user/logout')) {
      const tokenRefresh = await refreshToken(authToken)

      if (tokenRefresh != null) {
        const response = NextResponse.next({
          request: {
            headers: requestHeaders,
          },
        })
        response.cookies.set('la-token', tokenRefresh.refreshedToken, {
          path: '/',
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
        })
      }
    }

    return response
  }
}

export async function middleware(request: NextRequest) {
  const middlewares = [authMiddleware]
  for (const middlewareFn of middlewares) {
    const response = await middlewareFn(request)
    if (response) {
      return response
    }
  }
  return NextResponse.next()
}

// Configure middleware to run on specific paths
export const config = {
  matcher: [
    '/dashboard/:path*',
    '/api/:path*',
    '/((?!_next/static|_next/image|favicon.ico|media).*)',
  ],
}
