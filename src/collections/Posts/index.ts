import { CollectionConfig, User } from 'payload'
import { PublicUser } from '@/payload-types'
import isAdminOnly from '@/payloadCMS/roleaccess/isAdminOnly'
import isFieldAdminOnly from '@/payloadCMS/roleaccess/isFieldAdminOnly'
import calculateReadingTimeHook from '@/utilities/payload/calculateReadingTime'
import formatSlug from '../../utilities/payload/formatSlug'

const Posts: CollectionConfig = {
  slug: 'posts',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'category', 'publishDate', 'tags', 'status', 'readingTime'],
    group: 'Content',
  },
  access: {
    read: isAdminOnly,
    create: isAdminOnly,
    update: isAdminOnly,
    delete: isAdminOnly,
  },
  versions: {
    maxPerDoc: 10,
    drafts: true,
  },
  fields: [
    {
      name: 'title',
      label: 'Title',
      type: 'text',
      required: true,
    },
    {
      name: 'category',
      type: 'relationship',
      relationTo: 'categories',
      // limit the options using the below query which uses the "archive" field set in the categories collection
      filterOptions: {
        archived: { equals: false },
      },
      // allow selection of one or more categories
      hasMany: true,
    },
    {
      name: 'tags',
      type: 'relationship',
      relationTo: 'tags',
      // limit the options using the below query which uses the "archive" field set in the categories collection
      filterOptions: {
        archived: { equals: false },
      },
      // allow selection of one or more categories
      hasMany: true,
    },
    {
      name: 'image',
      label: 'Featured Image',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'summary',
      type: 'richText',
      label: 'Summary (keep it short, tldr style)',
    },
    {
      name: 'richText',
      type: 'richText',
      label: 'Content',
    },
    {
      name: 'readingTime',
      type: 'number',
      admin: {
        position: 'sidebar',
      },
      hooks: {
        beforeValidate: [calculateReadingTimeHook('richText')],
      },
    },
    {
      name: 'slug',
      label: 'Slug',
      type: 'text',
      admin: {
        position: 'sidebar',
      },
      hooks: {
        beforeValidate: [formatSlug('title')],
      },
    },
    {
      name: 'author',
      type: 'relationship',
      relationTo: 'users',
      defaultValue: ({ user }: { user: PublicUser | User }) => user.id,
      admin: {
        position: 'sidebar',
      },
      access: {
        update: isFieldAdminOnly,
      },
    },
    {
      name: 'publishDate',
      type: 'date',
      admin: {
        position: 'sidebar',
        description: 'Posts will not be public until this date',
      },
      defaultValue: () => new Date(),
    },
  ],
}

export default Posts
