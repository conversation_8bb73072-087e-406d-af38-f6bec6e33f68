import { FieldHook } from 'payload'
import { countTotalWords } from '../local/blog'

const calculateReadingTimeHook =
  (fallback: string): FieldHook =>
  ({ value, originalDoc, data }) => {
    if (value) {
      return value
    }

    const fallbackData = data?.[fallback] || originalDoc?.[fallback]

    const readingTime = Math.round(Math.max(countTotalWords(fallbackData.root.children) / 200, 1))

    return readingTime
  }

export default calculateReadingTimeHook
