import { isObjectEmpty } from '../object'

interface ChildrenNode {
  mode: string
  text: string
  type: string
  detail: number
  format: number
  version: number
}

interface RichTextNode {
  type: string
  tag?: string
  text?: string
  children?: ChildrenNode[]
}

export function getDescription(richText: RichTextNode[], wordLimit: number = 100): string {
  let totalWords: string[] = []
  for (const node of richText) {
    if (node.children) {
      node.children.forEach((child) => {
        if (child.type === 'text') {
          totalWords.push(...child.text.trim().split(/\s+/))
        }
      })
    }
  }
  return totalWords.slice(0, wordLimit).join(' ')
}

export function countTotalWords(richText: RichTextNode[]): number {
  let totalWords = 0
  for (const node of richText) {
    if (node.children) {
      node.children.forEach((child) => {
        if (child.type === 'text') {
          totalWords += child.text.trim().split(/\s+/).filter(Boolean).length
        }
      })
    }
  }

  return totalWords
}

export function queryBuilder(queryParams: { [key: string]: unknown }) {
  let finalQuery = {} as Record<string, any>
  Object.keys(queryParams).forEach((key: string) => {
    if (queryParams[key]) {
      finalQuery[key] = queryParams[key]
    }
  })

  if (isObjectEmpty(finalQuery)) {
    return finalQuery
  }

  return undefined
}

export default queryBuilder
