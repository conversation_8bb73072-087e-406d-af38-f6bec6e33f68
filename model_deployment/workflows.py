import subprocess
import json
import random
from pathlib import Path
import uuid
import shutil

import modal
from PIL import Image

from model_deployment.image_upload import upload_file_to_r2

from .app import app, vol
from .config import (
    CONTAINER_CPU, CONTAINER_MEMORY, CONTAINER_GPU, CONTAINER_TIMEOUT,
    MAX_CONTAINERS, SCALEDOWN_WINDOW, MAX_CONCURRENT_INPUTS,
    DEFAULT_WIDTH, DEFAULT_HEIGHT, DEFAULT_BATCH_SIZE, MAX_SEED_VALUE
)
from .utils import check_server_health, log_request_info, extract_object_key, find_node_by_type
from .models import Text2ImageRequestBody, ImagesResponse
from .api_handlers import validate_request_common


####################################Application####################################

@app.cls(
    cpu=CONTAINER_CPU,
    memory=CONTAINER_MEMORY,
    gpu=CONTAINER_GPU,
    timeout=CONTAINER_TIMEOUT,
    volumes={"/cache": vol},
    max_containers=MAX_CONTAINERS,
    scaledown_window=SCALEDOWN_WINDOW,
    enable_memory_snapshot=True,
)
@modal.concurrent(max_inputs=MAX_CONCURRENT_INPUTS)
class ComfyUI:
    port: int = 8000

    @modal.enter(snap=True)
    def launch_comfy_background(self):
        cmd = f"comfy launch --background -- --port {self.port}"
        subprocess.run(cmd, shell=True, check=True)

    @modal.enter(snap=False)
    def restore_snapshot(self):
        # initialize GPU for ComfyUI after snapshot restore
        # note: requires patching core ComfyUI, see the memory_snapshot_helper directory for more details
        import requests

        response = requests.post(f"http://127.0.0.1:{self.port}/cuda/set_device")
        if response.status_code != 200:
            print("Failed to set CUDA device")
        else:
            print("Successfully set CUDA device")

    @modal.method()
    def infer(self, workflow_path: str = "/root/flux_t2i_api.json"):
        # sometimes the ComfyUI server stops responding (we think because of memory leaks), so this makes sure it's still up
        self.poll_server_health()

        # runs the comfy run --workflow command as a subprocess
        cmd = f"comfy run --workflow {workflow_path} --wait --timeout 1200 --verbose"
        # subprocess.run(cmd, shell=True, check=True)

        try:
            result = subprocess.run(
                cmd,  # Replace with your command and arguments
                check=True,  # Raise an exception for non-zero exit codes
                stdout=subprocess.PIPE,      # Capture standard output
                stderr=subprocess.PIPE,      # Capture standard error
                text=True,                  # Return stdout and stderr as strings
                shell=True,
            )
            print("Command executed successfully.")
            print("Standard Output:", result.stdout)
        except subprocess.CalledProcessError as e:
            print(f"Command failed with exit code {e.returncode}.")
            print("Standard Output:", e.stdout)
            print("Standard Error:", e.stderr)
        except FileNotFoundError as e:
            print(f"File or directory not found: {e}")
        except Exception as e:
            print(f"An unexpected error occurred: {e}")

    @modal.asgi_app()
    def image_generation(self):
        from fastapi import FastAPI, Request, HTTPException, UploadFile, File, Form

        web_app = FastAPI()

        @web_app.post("/t2i")
        def t2i(request: Request, body: Text2ImageRequestBody):
            # handle request
            prompt = body.prompt
            batch_size = body.batch_size
            width = body.width
            height = body.height
            image_urls = body.image_urls
            
            # Validate request using common validation
            validate_request_common(prompt=prompt, image_urls=image_urls, batch_size=batch_size)

            # Log request using common logging
            client_host = request.client.host if request.client else "unknown"
            log_request_info("t2i", client_host,
                           prompt=prompt, batch_size=batch_size,
                           width=width, height=height)


            workflow_data = json.loads(
                (Path(__file__).parent / "flux_t2i_api.json").read_text()
            )

            # Find nodes dynamically
            empty_latent_node = find_node_by_type(workflow_data, "EmptyLatentImage")
            save_image_node = find_node_by_type(workflow_data, "SaveImage")
            k_sampler_node = find_node_by_type(workflow_data, "KSampler")
            text_encoder_node = find_node_by_type(workflow_data, "CLIPTextEncodeFlux")

            # Generate multiple images with different seeds
            images = []
            for i in range(batch_size):
                if text_encoder_node:
                    text_encoder_node["inputs"]["t5xxl"] = prompt

                if empty_latent_node:
                    empty_latent_node["inputs"]["width"] = width
                    empty_latent_node["inputs"]["height"] = height

                # give the output image a unique id per client request
                image_id = extract_object_key(image_urls[i])
                if save_image_node:
                    save_image_node["inputs"]["filename_prefix"] = image_id

                # set random seed
                random.seed()
                seed = random.randint(0, MAX_SEED_VALUE)
                if k_sampler_node:
                    k_sampler_node["inputs"]["seed"] = seed

                # save this updated workflow to a new file
                new_workflow_file = f"{image_id}.json"
                json.dump(workflow_data, Path(new_workflow_file).open("w"))

                # run inference on the currently running container
                self.infer.local(new_workflow_file)

                # upload output image, which stored in fixed location
                output_dir = "/root/comfy/ComfyUI/output"
                for file in Path(output_dir).iterdir():
                    if file.name.startswith(image_id):
                        metadata = upload_file_to_r2(
                            upload_url=image_urls[i],
                            file_path=file,
                            image_id=image_id,
                            width=width,
                            height=height
                        )
                        images.append(metadata)
   
            return ImagesResponse(images=images)

        @web_app.post("/i2i")
        def i2i(
            request: Request, 
            image: UploadFile = File(...), 
            prompt: str = Form(""),
            image_urls: str = Form(""),
            batch_size: int = Form(DEFAULT_BATCH_SIZE, ge=1),
            width: int = Form(DEFAULT_WIDTH, ge=1),
            height: int = Form(DEFAULT_HEIGHT, ge=1),
        ):
            # handle request
            client_host = request.client.host if request.client else "unknown"
            print(f"Received web request - prompt: {prompt}, batch_size: {batch_size}, width: {width}, height: {height} from {client_host}")

            if not image:
                 raise HTTPException(status_code=400, detail="Image file is required for image-to-image.")

            image_urls_list = image_urls.split(",")
            if len(image_urls_list) != batch_size:
                raise HTTPException(status_code=400, detail="Number of upload urls must match batch size.")

            # Save the uploaded image to the ComfyUI input directory
            comfy_input_dir = Path("/root/comfy/ComfyUI/input")
            image_filename = f"{uuid.uuid4().hex}_{image.filename}"
            destination_image_path = comfy_input_dir / image_filename
            try:
                with open(destination_image_path, "wb") as buffer:
                    shutil.copyfileobj(image.file, buffer)
            finally:
                image.file.close()

            workflow_data = json.loads(
                (Path(__file__).parent / "i2i_v3_api.json").read_text()
            )

            load_image_node = find_node_by_type(workflow_data, "LoadImage")
            save_image_node = find_node_by_type(workflow_data, "SaveImage")
            k_sampler_node = find_node_by_type(workflow_data, "KSamplerAdvanced")

            # Generate multiple images with different seeds
            images = []
            for i in range(batch_size):
                if load_image_node:
                    load_image_node["inputs"]["image"] = image_filename

                # give the output image a unique id per client request
                image_id = extract_object_key(image_urls_list[i])
                if save_image_node:
                    save_image_node["inputs"]["filename_prefix"] = image_id

                # set random seed
                random.seed()
                seed = random.randint(0, MAX_SEED_VALUE)
                if k_sampler_node:
                    k_sampler_node["inputs"]["noise_seed"] = seed

                # save this updated workflow to a new file
                new_workflow_file = f"{image_id}.json"
                json.dump(workflow_data, Path(new_workflow_file).open("w"))

                # run inference on the currently running container
                self.infer.local(new_workflow_file)

                # upload output image, which stored in fixed location
                output_dir = "/root/comfy/ComfyUI/output"
                for file in Path(output_dir).iterdir():
                    if file.name.startswith(image_id):
                        metadata = upload_file_to_r2(
                            upload_url=image_urls[i],
                            file_path=file,
                            image_id=image_id,
                            width=width,
                            height=height
                        )
                        images.append(metadata)

                # Clean up the temporary workflow file
                Path(new_workflow_file).unlink()

            # Clean up the uploaded image file after all inferences are done
            Path(destination_image_path).unlink()
            return ImagesResponse(images=images)


        @web_app.post("/rmbg")
        def rmbg(
            request: Request, 
            image: UploadFile = File(...),
            image_url: str = Form(""),
        ):
            # handle request
            client_host = request.client.host if request.client else "unknown"
            print(f"Received web request - rmbg from {client_host}")
            if not image:
                 raise HTTPException(status_code=400, detail="Image file is required for background removal.")

            if not image_url:
                raise HTTPException(status_code=400, detail="Upload url is required for background removal.")

            # Save the uploaded image to the ComfyUI input directory
            comfy_input_dir = Path("/root/comfy/ComfyUI/input")
            image_filename = f"{uuid.uuid4().hex}_{image.filename}"
            destination_image_path = comfy_input_dir / image_filename
            try:
                with open(destination_image_path, "wb") as buffer:
                    shutil.copyfileobj(image.file, buffer)
            finally:
                image.file.close()
            input_image = Image.open(destination_image_path)
            width, height = input_image.size

            workflow_data = json.loads(
                (Path(__file__).parent / "rmbg_api.json").read_text()
            )

            load_image_node = find_node_by_type(workflow_data, "LoadImage")
            save_image_node = find_node_by_type(workflow_data, "SaveImage")

            # Generate multiple images with different seeds
            images = []
            if load_image_node:
                load_image_node["inputs"]["image"] = image_filename

            # give the output image a unique id per client request
            image_id = extract_object_key(image_url)
            if save_image_node:
                save_image_node["inputs"]["filename_prefix"] = image_id

            # save this updated workflow to a new file
            new_workflow_file = f"{image_id}.json"
            json.dump(workflow_data, Path(new_workflow_file).open("w"))

            # run inference on the currently running container
            self.infer.local(new_workflow_file)

            # upload output image, which stored in fixed location
            output_dir = "/root/comfy/ComfyUI/output"
            for file in Path(output_dir).iterdir():
                if file.name.startswith(image_id):
                    metadata = upload_file_to_r2(
                        upload_url=image_url,
                        file_path=file,
                        image_id=image_id,
                        width=width,
                        height=height
                    )
                    images.append(metadata)

            # Clean up the temporary workflow file
            Path(new_workflow_file).unlink()

            # Clean up the uploaded image file after all inferences are done
            Path(destination_image_path).unlink()

            return ImagesResponse(images=images)

        return web_app


    def poll_server_health(self) -> None:
        """Check if the ComfyUI server is healthy and responding."""
        if not check_server_health(self.port):
            print("ComfyUI server is not healthy, stopping container")
            modal.experimental.stop_fetching_inputs()
            raise Exception("ComfyUI server is not healthy, stopping container")

